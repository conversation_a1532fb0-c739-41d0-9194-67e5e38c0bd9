global:
  name: workflow-engine
  description: workflow-engine
  maintainers:
    - email: <EMAIL>
      name: ludo
  labels:
    team: itsf

main:
  useConfigMap: false
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: IfNotPresent
    repository: nexus.itsf.io:5005/repository/maven-releases/workflow-engine
    tag: latest
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

configuration:
  env:
    SERVER_PORT: 8080
    SPRING_PROFILES_ACTIVE: ""
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: '5672'
    SPRING_RABBITMQ_VIRTUAL_HOST: /pa
    SPRING_RABBITMQ_SSL_KEYSTOREPASSWORD: ""
    SPRING_RABBITMQ_SSL_ENABLED: false
    SPRING_RABBITMQ_SSL_KEYSTORE: ""
    SPRING_DATASOURCE_URL: **************************,************:3306/workflow_engine?serverTimezone=Europe/Paris
    SPRING_DATASOURCE_HIKARI_MINIMUMIDLE: "3"
    WORKFLOW_ENGINE_DB_DRIVER: com.mariadb.jdbc.Driver
    SPRING_JPA_HIBERNATE_DDLAUTO: update

    TRANSACTION_ISOLATION: 2
    WORKFLOW_INTEGRATION_NB_OF_RETRIES: 3
    WORKFLOW_INTEGRATION_WAIT_FACTOR: 2
    WORKFLOW_INTEGRATION_WAIT_TIME_MS: 5

    # HIKARI
    # SPRING_DATASOURCE_HIKARI_MAXIMUMPOOLSIZE: "10"
    # SPRING_DATASOURCE_HIKARI_CONNECTIONTIMEOUT: "30000"
    # SPRING_DATASOURCE_HIKARI_MAXLIFETIME: "59000"
    # SPRING_DATASOURCE_HIKARI_IDLETIMEOUT: "59000"
    # SPRING_DATASOURCE_HIKARI_CONNECTIONINITSQL: "SET SESSION wait_timeout=60"

    # KEYCLOAK
    KEYCLOAK_RESOURCE: workflow-engine
    KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_CREDENTIALS_SECRET: '0123456789'
  envSecret:
    SPRING_DATASOURCE_USERNAME: maria-database/username
    SPRING_DATASOURCE_PASSWORD: maria-database/password
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password


# ############################################################################ #
#                                     DBMDL                                    #
# ############################################################################ #
dbmdl:
  envSecret:
    MIGRATIONS_USERNAME: maria-database/username
    MIGRATIONS_PASSWORD: maria-database/password
  image:
    repository: nexus.itsf.io:5005/monaco-telecom/workflow-engine-dbmdl
    tag: 1.1.2
  env:
    SPRING_DATASOURCE_URL: **************************,************:3306/workflow_engine
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXTS: "salt"
    SPRING_DATASOURCE_DRIVER_CLASS_NAME: org.mariadb.jdbc.Driver
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

