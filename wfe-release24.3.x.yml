# WF ENGINE :
- name: workflow-engine
  version: 3.3.2
  type: chart
  url: jfrog-artifactory.steelhome.internal/galaxion-docker/core/workflow-engine/workflow-engine/workflow-engine

- name: workflow-engine-liquibase
  version: 1.3.2
  type: chart
  url: jfrog-artifactory.steelhome.internal/galaxion-docker/core/workflow-engine/workflow-engine/workflow-engine-liquibase

- name: workflow-engine-cron-purge
  version: 1.3.2
  type: cron
  url: jfrog-artifactory.steelhome.internal/galaxion-docker/core/workflow-engine/workflow-engine/workflow-engine-cron-purge

# WF QUERY : 
- name: workflow-query
  version: 2.2.1
  type: chart
  url: jfrog-artifactory.steelhome.internal/galaxion-docker/core/workflow-engine/workflow-query/workflow-query

- name: workflow-query-liquibase
  version: 2.2.22
  type: chart
  url: jfrog-artifactory.steelhome.internal/galaxion-docker/core/workflow-engine/workflow-query/workflow-query-liquibase

- name: workflow-query-cron-purge
  version: 2.2.0
  type: cron
  url: jfrog-artifactory.steelhome.internal/galaxion-docker/core/workflow-engine/workflow-query/workflow-query-cron-purge

# OTHER :
- name: workflow-ui
  version: 1.18.6
  type: chart
  url: https://nexus.itsf.io/repository/helm-charts/

- name: workflow-connector-parent
  version: 3.1.13
  type: jar
  url: https://nexus.itsf.io/repository/maven-releases/
