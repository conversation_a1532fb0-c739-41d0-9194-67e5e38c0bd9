apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: keycloak-ingress
  namespace: glx-pa
  annotations:
    ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - glx-iam-pa-stg.tigo.cam
    secretName: wildcard-galaxion-tls
  rules:
  - host: glx-iam-pa-stg.tigo.cam
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: keycloak-http
            port:
              number: 8443
