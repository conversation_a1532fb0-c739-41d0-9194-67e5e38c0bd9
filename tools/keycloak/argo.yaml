apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: keycloak
  namespace: argocd
spec:
  project: glx-pa
  destination:
    server: https://kubernetes.default.svc
    namespace: glx-pa
  syncPolicy:
    automated:
      prune: false
      selfHeal: false
  sources:
  - chart: keycloak
    repoURL: https://codecentric.github.io/helm-charts
    targetRevision: 18.9.0
    ref: helm
    helm:
      valueFiles:
      - $values/tools/keycloak/values.yaml
  - repoURL: "https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git"
    targetRevision: stg
    ref: values