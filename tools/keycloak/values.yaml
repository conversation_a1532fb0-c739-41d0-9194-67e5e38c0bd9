postgresql:
  enabled: false
resources:
  limits:
    cpu: "1"
    memory: "2Gi"
  requests:
    cpu: "500m"
    memory: "1Gi"
extraEnv: |
  - name: JAVA_OPTS
    value: >-
      -Djava.net.preferIPv4Stack=true
      -Djboss.modules.system.pkgs=$JBOSS_MODULES_SYSTEM_PKGS
      -Djava.awt.headless=true
      -Dkeycloak.profile.feature.upload_scripts=enabled
  - name: DB_VENDOR
    value: mariadb
  - name: DB_ADDR
    value: "***********"
  - name: DB_PORT
    value: "3306"
  - name: DB_DATABASE
    value: galaxion_keycloak
  - name: DB_USER_FILE
    value: /secrets/db-creds/user
  - name: DB_PASSWORD_FILE
    value: /secrets/db-creds/password
  - name: JDBC_PARAMS
    value: "connectTimeout=59000"

extraVolumeMounts: |
  - name: db-creds
    mountPath: /secrets/db-creds
    readOnly: true

extraVolumes: |
  - name: db-creds
    secret:
      secretName: keycloak-db-secret

# ingress:
#   enabled: true
#   ingressClassName: "nginx"
image:
  pullPolicy: IfNotPresent
  repository: quay.io/keycloak/keycloak
  tag: 17.0.1-legacy
    
service:
  type: ClusterIP
