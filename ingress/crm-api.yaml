apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: crm-api-ingress
  namespace: glx-pa
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-headers: Content-Type, authorization, x-b3-traceid, x-b3-spanid, x-b3-parentspanid, x-b3-sampled, x-b3-flags, x-ot-span-context, galaxion-user-type, galaxion-user-identifier, galaxion-roles, galaxion-channels
    nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS, PATCH, DELETE
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  rules:
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: acquisition-prospects-service
            port:
              number: 8080
        path: /acquisition-prospects(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: catalog-service
            port:
              number: 8080
        path: /catalog(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: prospect-lead
            port:
              number: 8082
        path: /pl(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: tigo-sales-facade
            port:
              number: 8080
        path: /tigo-sales-facade(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: billing-cycles-service
            port:
              number: 8080
        path: /billing-cycles(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: tigo-b2c-crm-ui
            port:
              number: 8080
        path: /tigo-b2c-crm-ui(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: malta-custom-mock-service
            port:
              number: 8080
        path: /malta-address(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: malta-custom-mock-service
            port:
              number: 8080
        path: /malta-custom-mock(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: workflow-query
            port:
              number: 8080
        path: /workflow-query(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: search-engine-service
            port:
              number: 8080
        path: /search-engine(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: accounts-service
            port:
              number: 8080
        path: /subscription-management(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: billing-cycles-service
            port:
              number: 8080
        path: /billing-cycles(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: account-receivable-facade
            port:
              number: 8080
        path: /account-receivable-facade(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: collections-service
            port:
              number: 8080
        path: /collection(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: contacts-service
            port:
              number: 8080
        path: /contact-management(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: notifications-service
            port:
              number: 8080
        path: /notifications-service(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: customer-history
            port:
              number: 8080
        path: /customer-history(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: credit-scores-service
            port:
              number: 8080
        path: /credit-scores-service(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: dms
            port:
              number: 8080
        path: /document-management(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: geographic-management-service
            port:
              number: 8082
        path: /geographicManagement(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: tigo-crm-api-gateway
            port:
              number: 8080
        path: /api-gateway(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: carts-service
            port:
              number: 8080
        path: /cart(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: notifications-ui
            port:
              number: 8080
        path: /notifications-ui(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: customer-history-admin-ui
            port:
              number: 8080
        path: /customer-history-admin-ui(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: tigo-workflow-order-creator
            port:
              number: 8080
        path: /tigo-workflow-order-creator(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: workflow-engine-facade
            port:
              number: 8080
        path: /workflow-engine-facade(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: workflow-query
            port:
              number: 8080
        path: /workflow-query(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: workflow-engine
            port:
              number: 8080
        path: /workflow-engine(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: equipment-pa-service
            port:
              number: 8082
        path: /equipment-service(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: account-receivable-service
            port:
              number: 8080
        path: /ar(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: payments-service
            port:
              number: 8080
        path: /payments-service(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: payment-plan
            port:
              number: 8080
        path: /payment-plan(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
        - backend:
            service:
              name: financial-documents
              port:
                number: 8080
          path: /financial-documents(/|$)(.*)
          pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: notifications-service
            port:
              number: 8080
        path: /notifications-service(/|$)(.*)
        pathType: ImplementationSpecific
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: case-management-pa-ui
            port:
              number: 8080
        path: /case-management-ui(/|$)(.*)
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - glx-crm-pa-stg.tigo.cam
    secretName: wildcard-galaxion-tls
