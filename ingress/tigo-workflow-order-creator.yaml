apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tigo-workflow-order-creator
  namespace: glx-pa
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-headers: Content-Type, authorization, x-b3-traceid, x-b3-spanid, x-b3-parentspanid, x-b3-sampled, x-b3-flags, x-ot-span-context
    nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS, PATCH
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: tigo-workflow-order-creator-sit.tigo.com.co
    http:
      paths:
      - backend:
          service:
            name: tigo-workflow-order-creator
            port:
              number: 8080
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - tigo-workflow-order-creator-sit.tigo.com.co
    secretName: wildcard-galaxion-tls
