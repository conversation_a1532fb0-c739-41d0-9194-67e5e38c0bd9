apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: equipment-pa-service-ingress
  namespace: glx-pa
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-headers: Content-Type, authorization, x-b3-traceid, x-b3-spanid, x-b3-parentspanid, x-b3-sampled, x-b3-flags, x-ot-span-context
    nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/enable-cors: 'true'
spec:
  ingressClassName: nginx
  rules:
  - host: glx-equipment-pa-service.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: equipment-pa-service
            port:
              number: 8082
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - glx-equipment-pa-service.tigo.cam
    secretName: wildcard-galaxion-tls
