apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: equipments-seravice-ingress
  namespace: glx-pa
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-headers: Content-Type, authorization, x-b3-traceid, x-b3-spanid, x-b3-parentspanid, x-b3-sampled, x-b3-flags, x-ot-span-context, galaxion-channels, galaxion-roles, galaxion-user-identifier, galaxion-user-type
    nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS, PATCH
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/enable-cors: 'true'
spec:
  ingressClassName: nginx
  rules:
  - host: glx-equipments-seravice-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: equipments-seravice
            port:
              number: 8080
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - glx-equipments-seravice-pa-stg.tigo.cam
    secretName: wildcard-galaxion-tls
