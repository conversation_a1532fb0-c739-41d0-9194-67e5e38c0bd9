apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: geographic-management-mockup-ingress
  namespace: glx-pa
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-headers: Content-Type, authorization, x-b3-traceid, x-b3-spanid, x-b3-parentspanid, x-b3-sampled, x-b3-flags, x-ot-span-context
    nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS, PATCH
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: galaxion-geographic-management-mockup-sit.tigo.com.co
    http:
      paths:
      - backend:
          service:
            name: geographic-management-service
            port:
              number: 8082
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - galaxion-geographic-management-mockup-sit.tigo.com.co
    secretName: wildcard-galaxion-tls
