apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: billing-apigateway-ingress
  namespace: glx-pa
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-headers: DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-B3-Sampled,X-B3-SpanId,X-B3-TraceId
    nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS, PATCH, DELETE
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/enable-cors: 'true'
spec:
  ingressClassName: nginx
  rules:
  - host: glx-billing-apigateway-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: billing-apigateway
            port:
              number: 8080
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - glx-billing-apigateway-pa-stg.tigo.cam
    secretName: wildcard-galaxion-tls
