apiVersion: v1
data:
  tls.crt: 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
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  name: wildcard-galaxion-tls
  namespace: glx-pa
type: kubernetes.io/tls