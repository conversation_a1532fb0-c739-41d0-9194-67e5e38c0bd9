apiVersion: v1
kind: Service
metadata:
  name: ingress-ingress
  namespace: ingress-nginx
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
    nodePort: 32673
  - port: 443
    targetPort: 443
    protocol: TCP
    name: https
    nodePort: 32672
  selector:
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: ingress-nginx
    app.kubernetes.io/name: ingress-nginx
