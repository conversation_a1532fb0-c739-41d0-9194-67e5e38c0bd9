apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: service-coverage-mock-up
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: service-coverage-mock-up
    helm:
      valueFiles:
      - $values/microservices/mocks/service-coverage/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: "0.0.x-SNAPSHOT"
  - ref: values
    repoURL: "https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git"
    targetRevision: stg
