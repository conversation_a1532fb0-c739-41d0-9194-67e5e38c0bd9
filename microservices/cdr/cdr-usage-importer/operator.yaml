  apiVersion: argoproj.io/v1alpha1
  kind: Application
  metadata:
    name: cdr-spark-operator
    namespace: argocd
  spec:
    destination:
      namespace: cdr-pa
      server: https://kubernetes.default.svc
    project: glx-pa
    syncPolicy:
      automated:
        prune: true
        selfHeal: true
    sources:
    - chart: spark-operator
      helm:
        valueFiles:
        - $values/microservices/cdr/cdr-usage-importer/operator_values.yaml
      ref: helm
      repoURL: https://kubeflow.github.io/spark-operator  # repo oficial
      targetRevision: 2.2.1
    - ref: values
      repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
      targetRevision: stg