apiVersion: sparkoperator.k8s.io/v1beta2
kind: SparkApplication
metadata:
  name: cdr-usage-importer
  namespace: cdr-pa
  annotations:
    argocd.argoproj.io/sync-options: Replace=true,Prune=false
    argocd.argoproj.io/compare-options: IgnoreExtraneous
    argocd.argoproj.io/hook: Skip
spec:
  type: Python
  pythonVersion: "3"
  mode: cluster
  image: nexus-tsf.tigo.cam/glx/glx-pa/cdr/cdr-usage-importer:1.1.2
  imagePullPolicy: Always
  mainApplicationFile: local:///app/cdr-usage-importer/jobs/usage_importer.py
  sparkVersion: "3.4.1"
  deps:
    jars:
      - "local:///app/cdr-usage-importer/libs/postgresql-42.7.3.jar"
  restartPolicy:
    type: Never
    onFailureRetries: 0
  driver:
    cores: 1
    memory: "4g"
    serviceAccount: spark
    env:
      - name: DATABASE_HOST
        value: "*************"
      - name: DATABASE_PORT
        value: "5432"
      - name: DATABASE_USERNAME
        value: "postgres"
      - name: DATABASE_PASSWORD
        value: "Soporte2025!"
      - name: DATABASE_NAME
        value: "cdr-usage-consumption"
      - name: RABBITMQ_HOST
        value: "************"
      - name: RABBITMQ_PORT
        value: "5672"
      - name: RABBITMQ_USERNAME
        value: "admin"
      - name: RABBITMQ_PASSWORD
        value: "Soporte2025!"
      - name: RABBITMQ_VIRTUAL_HOST
        value: "/"
      - name: SPARK_USAGE_BRAND
        value: "EIR"
      - name: SPARK_USAGE_DOMAIN
        value: "MOBILE"
      - name: SPARK_USAGE_ACCOUNT_TYPE
        value: "B2C"
      - name: SPARK_USAGE_SUBSCRIPTION_TYPE
        value: "POSTPAY"
      - name: SPARK_APP_NAME
        value: "EIR_B2C_POSTPAY_MOBILE_UsageImporter.1.1.2"
      - name: EIR_B2C_POSTPAY_MOBILE_SOURCE_DIR
        value: "/data/usage/incoming/eir/b2c/postpay/mobile"
      - name: EIR_B2C_POSTPAY_MOBILE_ARCHIVE_DIR
        value: "/data/usage/archive/eir/b2c/postpay/mobile"
      - name: EIR_B2C_POSTPAY_MOBILE_ERROR_DIR
        value: "/data/usage/error/eir/b2c/postpay/mobile"
      - name: EIR_B2C_POSTPAY_MOBILE_CHECKPOINT_DIR
        value: "/data/spark/checkpoint/eir/b2c/postpay/mobile"
      - name: EIR_B2C_POSTPAY_MOBILE_LOG_DIR
        value: "/data/usage/logs/eir/b2c/postpay/mobile"
      - name: LOG_FILE_BACKUP_COUNT
        value: "7"
      - name: BATCH_SIZE
        value: "100"
  executor:
    cores: 1
    instances: 2
    memory: "4g"
    serviceAccount: spark
    env:
      - name: DATABASE_HOST
        value: "*************"
      - name: DATABASE_PORT
        value: "5432"
      - name: DATABASE_USERNAME
        value: "postgres"
      - name: DATABASE_PASSWORD
        value: "Soporte2025!"
      - name: DATABASE_NAME
        value: "cdr-usage-consumption"
      - name: RABBITMQ_HOST
        value: "************"
      - name: RABBITMQ_PORT
        value: "5672"
      - name: RABBITMQ_USERNAME
        value: "admin"
      - name: RABBITMQ_PASSWORD
        value: "Soporte2025!"
      - name: RABBITMQ_VIRTUAL_HOST
        value: "/"
      - name: SPARK_USAGE_BRAND
        value: "EIR"
      - name: SPARK_USAGE_DOMAIN
        value: "MOBILE"
      - name: SPARK_USAGE_ACCOUNT_TYPE
        value: "B2C"
      - name: SPARK_USAGE_SUBSCRIPTION_TYPE
        value: "POSTPAY"
      - name: SPARK_APP_NAME
        value: "EIR_B2C_POSTPAY_MOBILE_UsageImporter.1.1.2"
      - name: LOG_FILE_BACKUP_COUNT
        value: "7"
      - name: BATCH_SIZE
        value: "100"
  sparkConf:
    "spark.dynamicAllocation.enabled": "true"
    "spark.dynamicAllocation.minExecutors": "2"
    "spark.dynamicAllocation.maxExecutors": "12"
    "spark.dynamicAllocation.initialExecutors": "2"
    "spark.max.files.per.trigger": "1"
    "spark.log.level": "ERROR"
    "spark.streaming.trigger.processingTime": "5 seconds"
    "spark.driver.extraClassPath": "/app/cdr-usage-importer/libs/postgresql-42.7.3.jar"
    "spark.executor.extraClassPath": "/app/cdr-usage-importer/libs/postgresql-42.7.3.jar"
    "spark.usage.brand": "EIR"
    "spark.usage.domain": "MOBILE"
    "spark.usage.account.type": "B2C"
    "spark.usage.subscription.type": "POSTPAY"
    "spark.app.name": "EIR_B2C_POSTPAY_MOBILE_UsageImporter.1.1.2"
    "spark.executorEnv.DATABASE_HOST": "*************"
    "spark.executorEnv.DATABASE_PORT": "5432"
    "spark.executorEnv.DATABASE_USERNAME": "postgres"
    "spark.executorEnv.DATABASE_PASSWORD": "Soporte2025!"
    "spark.executorEnv.DATABASE_NAME": "cdr-usage-consumption"
    "spark.executorEnv.RABBITMQ_HOST": "************"
    "spark.executorEnv.RABBITMQ_PORT": "5672"
    "spark.executorEnv.RABBITMQ_USERNAME": "admin"
    "spark.executorEnv.RABBITMQ_PASSWORD": "Soporte2025!"
    "spark.executorEnv.RABBITMQ_VIRTUAL_HOST": "/"
    "spark.executorEnv.BATCH_SIZE": "100"
    "spark.driverEnv.DATABASE_HOST": "*************"
    "spark.driverEnv.DATABASE_PORT": "5432"
    "spark.driverEnv.DATABASE_USERNAME": "postgres"
    "spark.driverEnv.DATABASE_PASSWORD": "Soporte2025!"
    "spark.driverEnv.DATABASE_NAME": "cdr-usage-consumption"
    "spark.driverEnv.RABBITMQ_HOST": "************"
    "spark.driverEnv.RABBITMQ_PORT": "5672"
    "spark.driverEnv.RABBITMQ_USERNAME": "admin"
    "spark.driverEnv.RABBITMQ_PASSWORD": "Soporte2025!"
    "spark.driverEnv.RABBITMQ_VIRTUAL_HOST": "/"
    "spark.driverEnv.BATCH_SIZE": "100"
    "spark.executorEnv.EIR_B2C_POSTPAY_MOBILE_SOURCE_DIR": "/data/usage/incoming/eir/b2c/postpay/mobile"
    "spark.executorEnv.EIR_B2C_POSTPAY_MOBILE_ARCHIVE_DIR": "/data/usage/archive/eir/b2c/postpay/mobile"
    "spark.executorEnv.EIR_B2C_POSTPAY_MOBILE_ERROR_DIR": "/data/usage/error/eir/b2c/postpay/mobile"
    "spark.executorEnv.EIR_B2C_POSTPAY_MOBILE_CHECKPOINT_DIR": "/data/spark/checkpoint/eir/b2c/postpay/mobile"
    "spark.executorEnv.EIR_B2C_POSTPAY_MOBILE_LOG_DIR": "/data/usage/logs/eir/b2c/postpay/mobile"
    "spark.driverEnv.EIR_B2C_POSTPAY_MOBILE_SOURCE_DIR": "/data/usage/incoming/eir/b2c/postpay/mobile"
    "spark.driverEnv.EIR_B2C_POSTPAY_MOBILE_ARCHIVE_DIR": "/data/usage/archive/eir/b2c/postpay/mobile"
    "spark.driverEnv.EIR_B2C_POSTPAY_MOBILE_ERROR_DIR": "/data/usage/error/eir/b2c/postpay/mobile"
    "spark.driverEnv.EIR_B2C_POSTPAY_MOBILE_CHECKPOINT_DIR": "/data/spark/checkpoint/eir/b2c/postpay/mobile"
    "spark.driverEnv.EIR_B2C_POSTPAY_MOBILE_LOG_DIR": "/data/usage/logs/eir/b2c/postpay/mobile"
    "spark.kubernetes.driver.volumes.persistentVolumeClaim.data-vol.mount.path": "/data"
    "spark.kubernetes.driver.volumes.persistentVolumeClaim.data-vol.options.claimName": "spark-job-pvc"
    "spark.kubernetes.executor.volumes.persistentVolumeClaim.data-vol.mount.path": "/data"
    "spark.kubernetes.executor.volumes.persistentVolumeClaim.data-vol.options.claimName": "spark-job-pvc"