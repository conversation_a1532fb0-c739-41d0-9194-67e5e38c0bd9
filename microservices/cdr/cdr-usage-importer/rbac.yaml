apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: cdr-pa
  name: spark-operator-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "configmaps", "secrets", "events"]
  verbs: ["*"]
- apiGroups: ["sparkoperator.k8s.io"]
  resources: ["sparkapplications", "scheduledsparkapplications"]
  verbs: ["*"]
- apiGroups: ["batch", "extensions"]
  resources: ["jobs"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: spark-operator-rolebinding
  namespace: cdr-pa
subjects:
- kind: ServiceAccount
  name: spark-operator-controller
  namespace: cdr-pa
roleRef:
  kind: Role
  name: spark-operator-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: spark-cluster-admin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: spark
  namespace: cdr-pa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: spark-crd-access
  namespace: cdr-pa
rules:
  - apiGroups: ["sparkoperator.k8s.io"]
    resources: ["sparkapplications", "scheduledsparkapplications"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: spark-crd-access
  namespace: cdr-pa
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: spark-crd-access
subjects:
  - kind: ServiceAccount
    name: spark-operator-controller
    namespace: cdr-pa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: spark-operator-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: spark-operator-controller
  namespace: cdr-pa