apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cdr-usage-consumption-service
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: cdr-usage-consumption-service
    helm:
      valueFiles:
      - $values/microservices/cdr/cdr-usage-consumption-service/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: 1.7.0-SNAPSHOT-202505062114
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
