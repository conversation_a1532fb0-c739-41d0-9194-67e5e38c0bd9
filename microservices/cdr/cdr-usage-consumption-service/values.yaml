global:
  name: cdr-usage-consumption-service
  description: CDR repository service
# virtualservice:
#  gateways:
#  - galaxion-eir-dev-gateway-wildcard
#  hosts:
#  - "cdr-usage-consumption-service.eir-dev.itsf.io"
main:
  replicas: 1
  securityContext: enabled
  #noHealthProbes: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/glx/glx-pa/cdr/cdr-usage-consumption-service
    tag: 1.7.0-SNAPSHOT
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    audience: itsffr
    log-format: json
    netpol-vip-galaxioneir-dev: "true"
    netpol-galaxioneir-eir-svc-dev: "true"
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
dbmdl:
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "1024Mi"
  image:
    tag: 1.7.0-SNAPSHOT
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  labels:
    netpol-vip-galaxioneir-dev: "true"
    netpol-db-postgres-cdr-eir-dev: "true"
  env:
    LIQUIBASE_DATASOURCE_USERNAME: "postgres"
    LIQUIBASE_DATASOURCE_PASSWORD: "Soporte2025!"
    LIQUIBASE_DATASOURCE_URL: ******************************************************************************
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
configuration:
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### RABBIT ###
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    SPRING_RABBITMQ_USERNAME: "glxpa"
    SPRING_RABBITMQ_PASSWORD: "Soporte2025!"
    ### DATABASE ###
    SPRING_DATASOURCE_URL: ******************************************************************************
    SPRING_DATASOURCE_USERNAME: "postgres"
    SPRING_DATASOURCE_PASSWORD: "Soporte2025!"
    ### SPRING ADMIN ###
    SPRING_BOOT_ADMIN_CLIENT_ENABLED: true
    SPRING_BOOT_ADMIN_CLIENT_URL: http://actuator:8080
    SPRING_BOOT_ADMIN_CLIENT_INSTANCE_SERVICE_BASE_URL: http://cdr-usage-consumption-service.glx-pa.svc.cluster.local:8080
    ### METRIC ###
    MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "*"
    ### WEBSERVICES URLS ###
    WEBSERVICE_ACCOUNTS_URL: "http://accounts-service.glx-pa.svc.cluster.local:8080"
    WEBSERVICE_BILLING_CYCLE_URL: "http://billing-cycles-service.glx-pa.svc.cluster.local:8080"
    ## QUEUE
    QUEUE_BINDING_ARG_BRAND_1: GOMO
    QUEUE_BINDING_ARG_BRAND_2: EIR
    B2C_POSTPAY_MOBILE_USAGE_QUEUE_1: gomo.b2c.postpay.mobile.usage
    B2C_POSTPAY_MOBILE_USAGE_QUEUE_2: eir.b2c.postpay.mobile.usage
    B2C_PREPAY_MOBILE_USAGE_QUEUE: eir.b2c.prepay.mobile.usage
    B2B_POSTPAY_MOBILE_USAGE_QUEUE: eir.b2b.postpay.mobile.usage
    B2C_POSTPAY_FIXED_USAGE_QUEUE: eir.b2c.postpay.fixed.usage
    ## OTHERS
    BATCH_CHUNK_SIZE: 500
    RAW_USAGE_FETCH_SIZE: 500
    SERVICE_ERROR_RETRY_CRON_SCHEDULER_PATTERN: 0 0 * * * *
    USAGE_ERROR_RETRY_CRON_SCHEDULER_PATTERN: 0 0 * * * *
    BATCH_PURGE_CRON_SCHEDULER_PATTERN: 0 3 * * * *
    RAW_USAGE_STALLED_CRON_SCHEDULER_PATTERN: 0 10 * * * *
    RAW_USAGE_PURGE_CRON_SCHEDULER_PATTERN: 0 2 * * * *
    RAW_USAGE_PURGE_DAYS: 30
    SERVICE_ERROR_STALE_MONTHS: 1
    RAW_USAGE_STALLED_HOURS: 10
    BATCH_PURGE_HOURS: 1
    USAGE_ERROR_REPROCESS_DAYS: 2
    ALLOWED_SERVICE_DOMAIN_TYPES: DSL,ISDN,VOIP,PSTN,MOBILE
    FUTURE_PARTITION_MONTHS_COUNT: 6
    USAGE_PROCESSOR_ASYNC_RECORD_LIMIT: 50000
    ASYNC_EXECUTOR_THREAD_POOL_CORE_SIZE: 5
    ASYNC_EXECUTOR_THREAD_POOL_MAX_SIZE: 10
    ASYNC_EXECUTOR_THREAD_POOL_QUEUE_CAPACITY: 25
    ## SHEDLOCK CONFIGURATION
    BATCH_PURGE_LOCKED_ATLEAST_FOR: 2H
    BATCH_PURGE_LOCKED_ATMOST_FOR: 5H
    RAW_USAGE_STALLED_LOCKED_ATLEAST_FOR: 2H
    RAW_USAGE_STALLED_LOCKED_ATMOST_FOR: 5H
    RAW_USAGE_PURGE_LOCKED_ATLEAST_FOR: 2H
    RAW_USAGE_PURGE_LOCKED_ATMOST_FOR: 5H
    USAGE_ERROR_REPROCESS_LOCKED_ATLEAST_FOR: 2H
    USAGE_ERROR_REPROCESS_LOCKED_ATMOST_FOR: 5H
    SERVICE_ERROR_LOCKED_ATLEAST_FOR: 15M
    SERVICE_ERROR_LOCKED_ATMOST_FOR: 1H
    SERVICE_DAY_DIFFERENCE: "3"
