global:
  name: tecrep-equipments-service
  description: Tecrep Equipment Service
  maintainers:
    - email: micha<PERSON>.<EMAIL>
      name: <PERSON> LE JEUNE
  labels:
    team: itsf
main:
  useConfigMap: false
  replicas: 1
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion/tecrep-equipments-management
    tag: 2.27.1
  securityContext: disabled
#  securityContext: disabled
#  securityContext:
#    runAsUser: 1000
#    runAsGroup: 3000
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
#  labels:
#    owner: monacotelecom
#    log-format: json
 # certificates:
 #   caCertPath: /java/cacerts
 #   keystoreGeneratorImage: openjdk:17-jdk-slim
 #   secrets:
 #     - name: rabbitmq-cert
 #     - name: rabbitmq-p12
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "50m"
      memory: "256Mi"
configuration:
  envSecret:
    SPRING_DATASOURCE_USERNAME: maria-database/username
    SPRING_DATASOURCE_PASSWORD: maria-database/password
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password
  env:
    ### SERVER ####
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m -Djavax.net.ssl.trustStore=/java/cacerts
    SERVER_PORT: 8080
    SPRING_PROFILES_ACTIVE: output-logs-as-json,keycloak-multitenant,eir
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: "************:5672"
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    SPRING_RABBITMQ_VIRTUAL_HOST: /pa
    SPRING_RABBITMQ_SSL_KEYSTOREPASSWORD: ""
    SPRING_RABBITMQ_SSL_ENABLED: false
    SPRING_RABBITMQ_SSL_KEYSTORE: ""
    ### DATABASE ###
    DATASOURCE_DRIVER_CLASS_NAME: org.mariadb.jdbc.Driver
    DATASOURCE_URL: **************************,************:3306/tecrep_equipments_management
    SPRING_DATASOURCE_HIKARI_MINIMUMIDLE: "3"
    SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT: 30000
    SPRING_DATASOURCE_HIKARI_IDLE_TIMEOUT: 600000
    SPRING_DATASOURCE_HIKARI_MAXLIFETIME: "1800001"
    TECREP_EQM_DB_MAXIMUM_POOL_SIZE: 20
    TECREP_EQM_DB_MINIMUM_IDLE: 10
    TECREP_EQM_DDL_AUTO: none
    TECREP_EQM_GENERATE_DDL: true
    TECREP_EQM_MAX_FILE_SIZE: 10MB
    TECREP_EQM_MAX_REQUEST_SIZE: 10MB
    ### KEYCLOAK ###
    KEYCLOAK_RESOURCE: tecrep-equipments-management
    KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_CREDENTIALS_SECRET: '0123456789'
    ### OTHERS ###
    TECREP_EQM_EXPORT_DIR: /data/export_sim
    TECREP_EQM_IMPORT_SIM_DIR: /data/import_sim
    TECREP_EQM_IMPORT_SMTP_FROM_DOMAIN: "@tigo.com.gt"
    TABS_URI: https://xxx.mt/Msisdn
    IFS_URI: https://xxx.mt/Msisdn
    TECREP_EQM_EMAIL_EXCHANGE: notification-center
    TECREP_EQM_EMAIL_QUEUE: notification.request
    INFO_APP_DEPLOYEDFOR: TIGO

# ############################################################################ #
#                                     DBMDL                                    #
# ############################################################################ #
dbmdl:
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion/tecrep-equipments-management-liquibase-dbmdl
    tag: 2.22.0
#  labels:
#    owner: monacotelecom
  envFrom:
    - secretRef:
        name: maria-database
  env:
    SPRING_DATASOURCE_URL: **************************,************:3306/tecrep_equipments_management
