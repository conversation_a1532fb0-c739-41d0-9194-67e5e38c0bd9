apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tecrep-pa-equipments-management-webservice
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: common
    helm:
      valueFiles:
      - $values/microservices/tecrep/tecrep-equipments-management-webservice/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    targetRevision: 1.x.x
  - ref: values
    repoURL: "https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git"
    targetRevision: stg
