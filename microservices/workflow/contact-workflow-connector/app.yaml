apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tigo-contact-workflow-connector
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
  sources:
  - chart: contact-workflow-connector
    helm:
      valueFiles:
      - $values/microservices/workflow/contact-workflow-connector/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: "1.x.x-SNAPSHOT"
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
