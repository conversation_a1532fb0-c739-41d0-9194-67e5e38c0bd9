configuration:
  env:
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CREDENTIALS_SECRET: '0123456789'
    KEYCLOAK_RESOURCE: galaxion-workflow-connectors
    ### WEBSERVICES URLS ###
    WEBSERVICE_CREDIT_SCORES_URL: credit-scores-service:8080
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  useConfigMap: true
  resources:
    limits:
      cpu: "1"
      memory: "2048Mi"
    requests:
      cpu: "1"
      memory: "1024Mi"
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/credit-scores-workflow-connector
    tag: 1.0.0-SNAPSHOT