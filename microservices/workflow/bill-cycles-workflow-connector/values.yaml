configuration:
  env:
    ### WEBSERVICES URLS ###
    WEBSERVICE_BILLING_CYCLES_URL: billing-cycles-service:8080
    WEBSERVICE_ACCOUNT_URL: accounts-service:8080
    WEBSERVICE_WORKFLOW_ENGINE_URL: workflow-engine:8080
    WEBSERVICE_WORKFLOW_QUERY_URL: workflow-query:8080
    ## Keycloack
    KEYCLOAK_CREDENTIALS_SECRET: '**********'
    KEYCLOAK_RESOURCE: galaxion-workflow-connectors
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/bill-cycles-workflow-connector
    tag: 1.0.1-SNAPSHOT
  useConfigMap: true
  resources:
    limits:
      cpu: "1"
      memory: "2048Mi"
    requests:
      cpu: "500m"
      memory: "512Mi"
