configuration:
  env:
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CREDENTIALS_SECRET: '**********'
    KEYCLOAK_RESOURCE: galaxion-workflow-connectors
    WEBSERVICE_ACCOUNTS_SERVICE_URL: accounts-service:8080
    TZ: America/Guatemala
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - configMapRef:
        name: keycloak-access

main:
  useConfigMap: true
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/addons-workflow-connector
    tag: 1.0.0-SNAPSHOT
  port: 8080
  aliveInitialDelaySeconds: 17
  alivePeriodSeconds: 30
  readyInitialDelaySeconds: 17
  readyPeriodSeconds: 30
  resources:
    limits:
      cpu: "1"
      memory: "2048Mi"
    requests:
      cpu: "1"
      memory: "1024Mi"