configuration:
  env:
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CREDENTIALS_SECRET: '0123456789'
    KEYCLOAK_RESOURCE: galaxion-workflow-connectors
    ### WEBSERVICES URLS ###
    WEBSERVICE_COLLECT_URL: https://gatewayhome.tigo.com.co:30443/collectionapi
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/workflow-engine/collection-management-connector
    tag: 1.0.0-SNAPSHOT
  useConfigMap: true
  resources:
    limits:
      cpu: "500m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"