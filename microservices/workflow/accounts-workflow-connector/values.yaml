configuration:
  env:
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CREDENTIALS_SECRET: '**********'
    K<PERSON><PERSON>CLOAK_RESOURCE: galaxion-workflow-connectors
    TZ: America/Bogota
    ### WEBSERVICES URLS ###
    WEBSERVICE_SALESFACADE_URL: tigo-sales-facade:8080
    WEBSERVICE_CATALOG_URL: http://catalog-service:8080
    WEBSERVICE_ACCOUNT_URL: http://accounts-service:8080
    WEBSERVICE_WORKFLOWENGINE_URL: http://workflow-engine:8080
    WEBSERVICE_ECC_URL: http://early-cease-charges-service:8080
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/accounts-workflow-connector
    tag: 1.0.1-SNAPSHOT-TSFD-10999
  useConfigMap: true
  resources:
    limits:
      cpu: "1"
      memory: "2048Mi"
    requests:
      cpu: "500m"
      memory: "512Mi"
