configuration:
  env:
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_CREDENTIALS_SECRET: '**********'
    <PERSON><PERSON><PERSON><PERSON>OAK_RESOURCE: workflow-engine
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_AUTHORIZATIONGRANTTYPE: client_credentials
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: galaxion-notifications
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: https://glx-iam-pa-stg.tigo.cam/auth
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    WEBSERVICE_NOTIFICATIONSSERVICE_URL: notifications-service.glx-pa.svc:8080
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - configMapRef:
        name: keycloak-access

main:
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/notifications-workflow-connector
    tag: 1.0.1-SNAPSHOT
  resources:
    limits:
      cpu: "1"
      memory: "2048Mi"
    requests:
      cpu: "1"
      memory: "1024Mi"