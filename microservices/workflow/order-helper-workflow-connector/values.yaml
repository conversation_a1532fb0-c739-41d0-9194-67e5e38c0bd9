configuration:
  env:
    ### K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_CREDENTIALS_SECRET: '**********'
    <PERSON><PERSON><PERSON><PERSON><PERSON>K_RESOURCE: workflow-engine
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_AUTHORIZATIONGRANTTYPE: client_credentials
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: galaxion-malta-workflow-engine-facade
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: https://glx-iam-pa-stg.tigo.cam/auth
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    ### WEBSERVICES URLS ###
    WEBSERVICE_SALESFACADE_URL: tigo-sales-facade:8080
    WEBSERVICE_PROSPECT_URL: acquisition-prospects-service:8080
    WEBSERVICE_PROSPECTLEAD_URL: prospect-lead:8082
    WEBSERVICE_CART_URL: carts-service:8080
    WEBSERVICE_CATALOG_URL: catalog-service:8080
    WEBSERVICE_ACCOUNT_URL: accounts-service:8080
    WEBSERVICE_WORKFLOWENGINEFACADE_URL: workflow-engine-facade:8080
    WEBSERVICE_TECREPEQUIPMENTSERVICE_URL: tecrep-equipments-management-service:8080
    WEBSERVICE_WORKFLOWQUERY_URL: workflow-query:8080
    WEBSERVICE_WORKFLOWENGINE_URL: http://workflow-engine:8080
    ### CONSTANTS
    ADDONS_EQUIPMENT: MATERIAL
    ADDONS_CONTENT: OTT,TV_CHANNEL,ADDON_VIX
    ADDONS_EQUIPMENTCATALOGCODE: WIFI_PLUS_BASICO,WIFI_PLUS_AVANZADO,WIFI_PLUS_BASICO_FREE,WIFI_PLUS_EXTENSOR,CAJA_STICKTV,CAJA_ANDROID,CAJA_HD,ONT_INTERNET
    EQUIPMENT_EXCLUDECATALOGCODES: ONT,ONT_INTERNET
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/order-helper-workflow-connector
    tag: 2.0.1-SNAPSHOT-10554

  useConfigMap: true
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "750m"
      memory: "512Mi"
  #aliveInitialDelaySeconds: 90
  #alivePeriodSeconds: 90
  #readyInitialDelaySeconds: 90
  #readyPeriodSeconds: 90
