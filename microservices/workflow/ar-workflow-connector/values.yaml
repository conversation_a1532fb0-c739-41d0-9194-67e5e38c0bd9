configuration:
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m -Duser.timezone=Europe/Paris
    SERVER_PORT: 8080
    ### KEYCLOACK ###
    KEYCLOAK_CREDENTIALS_SECRET: '**********'
    <PERSON><PERSON><PERSON>CLOAK_RESOURCE: galaxion-workflow-connectors
    ### WEBSERVICES URLS ###
    WEBSERVICE_ACCOUNT_URL: account-receivable-facade:8080
    ### RABBITMQ ###
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
  envSecret:
    SPRING_RABBITMQ_USERNAME: "rabbitmq-creds/SPRING_RABBITMQ_USERNAME"
    SPRING_RABBITMQ_PASSWORD: "rabbitmq-creds/SPRING_RABBITMQ_PASSWORD"
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/ar-workflow-connector
    tag: 1.0.0-SNAPSHOT
  useConfigMap: true
  resources:
    limits:
      cpu: "500m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
