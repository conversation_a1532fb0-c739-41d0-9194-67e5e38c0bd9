configuration:
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m -Duser.timezone=Europe/Paris
    SERVER_PORT: 8080
    KEYCLOAK_CREDENTIALS_SECRET: '0123456789'
    KEYCLOAK_RESOURCE: workflow-engine-workflow-connector
    ### WEBSERVICES URLS ###
    WEBSERVICE_SALESFACADE_URL: tigo-sales-facade:8080
    WEBSERVICE_CREATEAPPOINTMENT_URL: appointments-service:8080
    ### VARIABLES ###
    MC_CUSTOMER_ASSET_DESCRIPTION_INTERNET: CGA2121+MTA_DATOS+TECHNICOLOR,CGNV5+MTA_DATOS+HITRON,TG2482+MTA_DATOS+ARRIS,CGNV5-U+MTA_DATOS+HITRON
    MC_CUSTOMER_ASSET_DESCRIPTION_TV: CGA2121+MTA_DATOS+TECHNICOL<PERSON>,CGNV5+MTA_DATOS+HITRON,TG2482+MTA_DATOS+ARRIS,CGNV5-U+M<PERSON>_DATOS+HITRON
    ### TELEPHONY ###
    FIELDSERVICE_AREACODE: 507
    FIELDSERVICE_TELEPHONYSERVICENAMEDESC: Telefonía
    ### LOGS
    LOGGING_LEVEL_ROOT: DEBUG
    ### FIELD-SERVICE ###
    FIELDSERVICE_EQUIPMENTADDON: MATERIAL

  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  useConfigMap: true
  replicas: 1
  securityContext: disabled
  #imagePullSecret: nexus-local
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/field-service-connector
    tag: 1.1.3-SNAPSHOT-10915
  resources:
    limits:
      cpu: "500m"
      memory: "1024Mi"
    requests:
      cpu: "100m"
      memory: "512Mi"
