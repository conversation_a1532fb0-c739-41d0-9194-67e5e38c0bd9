configuration:
  env:
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CREDENTIALS_SECRET: '0123456789'
    KEYCLOAK_RESOURCE: galaxion-workflow-connectors
    TZ: America/Bogota
    ### WEBSERVICES URLS ###
    WEBSERVICE_ORDER_URL: http://order-status-facade:8080
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/order-status-connector
    tag: 1.0.0-SNAPSHOT-10271-2
  useConfigMap: true
  resources:
    limits:
      cpu: "1"
      memory: "2048Mi"
    requests:
      cpu: "500m"
      memory: "512Mi"
