global:
  name: symphonica-workflow-connector
  description: Symphonica Workflow Connector

main:
  useConfigMap: true
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/symphonica-workflow-connector
    tag: 1.2.3-SNAPSHOT-10569-2
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: risf
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
configuration:
  envSecret:
    SPRING_RABBITMQ_USERNAME: "rabbitmq-creds/SPRING_RABBITMQ_USERNAME"
    SPRING_RABBITMQ_PASSWORD: "rabbitmq-creds/SPRING_RABBITMQ_PASSWORD"
  env:
    proxy.use-proxy: "false"
    proxy.host: ""
    proxy.port: ""
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### RABBITMQ ###
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    ### KEYCLOAK ###
    KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_SSL_REQUIRED: external
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_RESOURCE: workflow-engine-workflow-connector
    KEYCLOAK_CREDENTIALS_SECRET: "**********"
    ### SPRING_SECURITY ###
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_AUTHORIZATIONGRANTTYPE: client_credentials
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: galaxion-malta-workflow-engine-facade
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: https://glx-iam-pa-stg.tigo.cam/auth
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token

    ### WEBSERVICES URLS 1.1.0-SNAPSHOT ###
    WEBSERVICE_WORKFLOW_ENGINE_URL: workflow-engine:8080
    WEBSERVICE_EQUIPMENT_SERVICE_URL: http://equipments-pa-service:8082
    WEBSERVICE_CBS_URL: http://microcks.microcks.svc.cluster.local:8080/rest/CBS+Business+Control+management+API./1.0.0
    WEBSERVICE_SYMPHONICA_BASEURL: http://castlemock.mocks.svc:8080/castlemock/mock/rest/project/4dPPA4/application
    WEBSERVICE_SYMPHONICA_LOGIN_URL: /4UUjBQ/login
    WEBSERVICE_SYMPHONICA_REGISTRATION_URL: /4UUjBQ # ?
    WEBSERVICE_PHONERESERVATION_BASEURL: http://castlemock.mocks.svc:8080/castlemock/mock/rest/project/XkeJHd/application
    WEBSERVICE_PHONERESERVATION_TOKEN_URL: /KNYMIv
    WEBSERVICE_PHONERESERVATION_TARIFICACION_URL: /4nOrwf
    WEBSERVICE_PHONERESERVATION_RESOURCES_URL: /cghS0V/api/v2/private/auth/numbers
    WEBSERVICE_SYMPHONICAAIR_BASEURL: http://castlemock.mocks.svc:8080/castlemock/mock/rest/project/MOEo6z/application/aaDUSS/
    ### PHONE_RESERVATION
    PHONERESERVATION_TOKEN_AUTHORIZATIONGRANTTYPE: client_credentials
    PHONERESERVATION_TOKEN_CLIENTID: resources-management
    PHONERESERVATION_TOKEN_CLIENTSECRET: '8uY3MI56tPsxBYjaABPwf0MpqmJOYGY3'
    PHONERESERVATION_RESOURCES_ACTIVITYSERVICE: TELEPHONY
    PHONERESERVATION_RESOURCES_STATUSPHONENUMBER: AVAILABLE
    PHONERESERVATION_TARIFICATION_APIKEY: lFLq1V5wO7IhVIyUZpKq3BlhQoxITr1Vkxc5N9OZUzfx1FYuw0wScWy23amwrTOIjDJbmJlYSYTGowukSa1yOjScFhokqQPzuOZCnOHtMiCXp7QJs90uYtYmFwMdZ8vT
    ### SYMPHONICA
    SYMPHONICA_LOGIN_USERNAME: 'TIGOAPI_GALAXION_PA'
    SYMPHONICA_LOGIN_PASSWORD: 'Tig0_G$L&xiO_PA'
    SYMPHONICA_TEST: true
    SYMPHONICA_ALCUENTA_ANDROIDINTERFACENETWORKTYPE: 'INTERFACE_ANDROID'
    SYMPHONICA_ALCUENTA_STICKINTERFACENETWORKTYPE: 'INTERFACE_ANDROID'
    SYMPHONICA_ALSTV_INTERFACENETWORKTYPE: 'INTERFACE_STICK'
    ### TV
    TV_BOX_TYPE_CODES_ANDROID: "ANDROID_BOX,CAJA_ANDROID,CAJA_ANDROID_II"
    TV_BOX_TYPE_CODES_STICK: "STICK_TV,CAJA_STICKTV,CAJA_STICKTV_II"
    ### LOGS ?
    FEIGN_CLIENT_CONFIG_DEFAULT_LOGGERLEVEL: FULL
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS_CLIENT_MESSAGETRACING_SENT: TRACE
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS_CLIENT_MESSAGETRACING_RECEIVED: TRACE
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_INTERNAL_WS_TRANSPORT_HTTP_CLIENT_HTTPTRANSPORTPIPE: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_WS_TRANSPORT_HTTP_HTTPADAPTER: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_WS_CLIENT_SEI: DEBUG
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB_CLIENT_RESTTEMPLATE: DEBUG
    LOGGING_LEVEL_ORG_APACHE_HTTP: DEBUG
    LOGGING_LEVEL_ORG_APACHE_HTTP_WIRE: DEBUG
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access
