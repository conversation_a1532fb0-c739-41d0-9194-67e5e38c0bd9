global:
  name: cbs-workflow-connector
  description: CBS Workflow Connector

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/cbs-workflow-connector
    tag: 1.0.0-SNAPSHOT
  useConfigMap: true
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: risf
  resources:
    limits:
      cpu: "500m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  aliveInitialDelaySeconds: 90
  alivePeriodSeconds: 90
  readyInitialDelaySeconds: 90
  readyPeriodSeconds: 90

configuration:
  envSecret:
    SPRING_RABBITMQ_USERNAME: "rabbitmq-creds/SPRING_RABBITMQ_USERNAME"
    SPRING_RABBITMQ_PASSWORD: "rabbitmq-creds/SPRING_RABBITMQ_PASSWORD"
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m -Dhttps.protocols=SSLv3
    SERVER_PORT: 8080
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### RABBITMQ ###
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    ### KEYCLOAK ###
    KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_SSL_REQUIRED: external
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_RESOURCE: workflow-engine-workflow-connector
    KEYCLOAK_CREDENTIALS_SECRET: "**********"
    ### WEBSERVICES URLS ###
    WEBSERVICE_CBS_URL: http://castlemock.mocks.svc:8080/castlemock/mock/rest/project/9Ew0oE/application/zTJMMV
    ### LOGS
    FEIGN_CLIENT_CONFIG_DEFAULT_LOGGERLEVEL: FULL
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS_CLIENT_MESSAGETRACING_SENT: TRACE
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS_CLIENT_MESSAGETRACING_RECEIVED: TRACE
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_INTERNAL_WS_TRANSPORT_HTTP_CLIENT_HTTPTRANSPORTPIPE: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_WS_TRANSPORT_HTTP_HTTPADAPTER: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_WS_CLIENT_SEI: DEBUG
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB_CLIENT_RESTTEMPLATE: DEBUG
    LOGGING_LEVEL_ORG_APACHE_HTTP: DEBUG
    LOGGING_LEVEL_ORG_APACHE_HTTP_WIRE: DEBUG
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access
