global:
  name: equipments-workflow-connector
  description: Equipments Workflow Connector
  maintainers:
  - email: jero<PERSON>.be<PERSON><PERSON>@risf.io
    name: <PERSON>
  - email: <EMAIL>
    name: Ludovic Cuny
  labels:
    team: risf

main:
  useConfigMap: true
  replicas: 1
  securityContext: disabled
  #imagePullSecret: nexus-local
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/orchestration-tools/workflow-engine/connector/equipments-workflow-connector
    tag: 1.4.0-SNAPSHOT-10879
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: risf
  resources:
    limits:
      cpu: "500m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  healthProbes:
    livenessProbe:
      httpGet:
        path: /actuator/health
        port: 8080
      initialDelaySeconds: 180
      periodSeconds: 30
    readinessProbe:
      httpGet:
        path: /actuator/health
        port: 8080
      initialDelaySeconds: 180
      periodSeconds: 30
      successThreshold: 1
      timeoutSeconds: 10

configuration:
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    SPRING_PROFILES_ACTIVE: ''
    ### KEYCLOAK ###
    KEYCLOAK_SSL_REQUIRED: external
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_RESOURCE: workflow-engine-workflow-connector
    KEYCLOAK_CREDENTIALS_SECRET: '**********'
    KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    ### WEBSERVICES URLS ###
    WEBSERVICE_EQUIPMENT_URL: http://equipments-pa-service:8082
    WEBSERVICE_EQUIPMENTTECREP_URL: http://equipments-pa-service:8082
    WEBSERVICE_ACCOUNT_URL: accounts-service:8080
    WEBSERVICE_WORKFLOWENGINE_URL: workflow-engine:8080
    WEBSERVICE_ANCILLARYEQUIPMENTS_URL: http://microcks.mocks.svc:8080/rest/API+Techrep+-****/1.0.0

    ### WEBSERVICES PHONENUMBERSERVICES ###
    WEBSERVICE_PHONENUMBERSERVICES_BASEURL: http://castlemock.mocks.svc:8080/castlemock/mock/rest/project/XkeJHd/application/cghS0V
    WEBSERVICE_PHONENUMBERSERVICES_RESOURCES_CREATEANDACTIVATEURL: /services/createandactivate
    WEBSERVICE_PHONENUMBERSERVICES_RESOURCES_ACTIVATEURL: /numbers/{phonenumber}/activate
    WEBSERVICE_PHONENUMBERSERVICES_RESOURCES_RELEASENUMBERURL: /numbers/{phonenumber}/free

    CONFIG_ADDONEQUIPMENTSTYPE: MATERIAL
    ### CONFIG PHONENUMBERACTIVATIONPARAMS ###
    CONFIG_PHONENUMBERACTIVATIONPARAMS_ACTIVITYSERVICE: TELEPHONY
    CONFIG_PHONENUMBERACTIVATIONPARAMS_ACCESSTYPE: EMTA
    CONFIG_PHONENUMBERACTIVATIONPARAMS_SERVICECATEGORY: ACCESS
    ### SPRING_SECURITY ###
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_AUTHORIZATIONGRANTTYPE: client_credentials
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: galaxion-malta-workflow-engine-facade
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: https://glx-iam-pa-stg.tigo.cam/auth
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token

