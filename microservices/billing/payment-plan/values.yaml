main:
  useConfigMap: true
  replicas: 1
  securityContext: enabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/glx/itsf-team/payment-plan
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  aliveInitialDelaySeconds: 90
  alivePeriodSeconds: 90
  readyInitialDelaySeconds: 90
  readyPeriodSeconds: 90
  labels:
    owner: tsf
  resources:
    limits:
      cpu: "500m"
      memory: "2Gi"
    requests:
      cpu: "50m"
      memory: "1Gi"

dbmdl:
  resources:
    limits:
      cpu: "300m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  command: [ "sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$DATASOURCE_USERNAME --password=$DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update" ]
  labels:
    owner: itsf
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,172.16.79.10:3306/payment-plan
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    GALAXION_USER_IDENTIFIER: SYSTEM
    GALAXION_USER_TYPE: SYSTEM

  envFrom:
  - secretRef:
      name: maria-database

configuration:
  envFrom:
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: keycloak-access
  env:
    FEIGN_CLIENT_CONFIG_DEFAULT_LOGGERLEVEL: FULL
    ### HEADERS ###
    GALAXION_USER_IDENTIFIER: SYSTEM
    GALAXION_USER_TYPE: SYSTEM
    ### SERVER ###
    JAVA_TOOL_OPTIONS: "-Xms1024m -Xmx1024m -Djavax.net.debug=ssl:handshake:verbose"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,172.16.79.10:3306/payment-plan
    ### SPRING ADMIN ###
    SPRING_BOOT_ADMIN_CLIENT_ENABLED: true
    SPRING_BOOT_ADMIN_CLIENT_URL: http://payment-plan:8080
    SPRING_BOOT_ADMIN_CLIENT_INSTANCE_SERVICE_BASE_URL: http://payment-plan:8080
#    SPRINGDOC_SWAGGER_UI_PATH: /payment-plan/swagger-ui.html
#    SPRINGDOC_API_DOCS_PATH: /payment-plan/v3/api-docs
