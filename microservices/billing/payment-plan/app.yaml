apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: payment-plan
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
  sources:
  - chart: payment-plan
    helm:
      valueFiles:
      - $values/microservices/billing/payment-plan/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: "1.1.0-SNAPSHOT"
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
