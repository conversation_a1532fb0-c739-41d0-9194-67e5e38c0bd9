main:
  useConfigMap: true
  replicas: 1
  securityContext: enabled
  port: 8080
  alivePath: /__health
  readyPath: /__health
  labels:
    owner: tsf
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/galaxion/galaxion-core-api-gateway
    tag: 1.6.0
configuration:
  env:
    KRAKEND_AUTH_VALIDATOR_JWK_URL: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/certs
    KRAKEND_AUTH_VALIDATOR_TLS_CA_PATH: /etc/krakend/certificates/root.pem
    KRAKEND_AUTH_VALIDATOR_DEBUG: true
    KRAKEND_AUTH_VALIDATOR_CLIENT_ID: galaxion-core-api-gateway
    KRAKEND_AUTH_BASE_URL: https://glx-iam-pa-stg.tigo.cam

    ### Logging ###
    KRAKEND_LOG_LEVEL: DEBUG