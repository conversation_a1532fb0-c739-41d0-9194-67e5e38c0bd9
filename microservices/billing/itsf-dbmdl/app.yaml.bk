apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: itsf-dbmdl
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
  sources:
  - chart: itsf-dbmdl
    helm:
      valueFiles:
      - $values/microservices/billing/itsf-dbmdl/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: "1.2.0-SNAPSHOT"
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
