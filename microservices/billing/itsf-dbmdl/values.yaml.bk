main:
  useConfigMap: true
  replicas: 1
  securityContext: enabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/glx/itsf-team/itsf-dbmdl
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  aliveInitialDelaySeconds: 90
  alivePeriodSeconds: 90
  readyInitialDelaySeconds: 90
  readyPeriodSeconds: 90
  labels:
    owner: tsf
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

dbmdl:
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  command: [ "sh", "-c", "docker-entrypoint.sh --url=$DATASOURCE_URL --username=$DATASOURCE_USERNAME --password=$DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$DATASOURCE_CHANGELOGFILE update -Daccount.schema=$MIGRATIONS_ACCOUNT_SCHEMA -Dbilling.cycle.schema=$MIGRATIONS_CYCLE_SCHEMA -Dcontact.schema=$MIGRATIONS_CONTACT_SCHEMA -Daccount.receivable.schema=$MIGRATIONS_ACCOUNT_RECEIVABLE_SCHEMA -Daddress.schema=$MIGRATIONS_ADDRESS_SCHEMA -Dbilling_view.schema=$BILLING_GALAXION_SCHEMA" ]
  labels:
    owner: itsf
  env:
    # MariaDB
    DATASOURCE_USERNAME: glx-pa
    DATASOURCE_PASSWORD: Soporte2025!
    DATASOURCE_URL:  **************************,************:3306/itsf-dbmdl
    DATASOURCE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    # Schemas
    MIGRATIONS_ACCOUNT_SCHEMA: accounts
    MIGRATIONS_CYCLE_SCHEMA: billing_cycles
    MIGRATIONS_CONTACT_SCHEMA: contacts
    MIGRATIONS_ACCOUNT_RECEIVABLE_SCHEMA: account_receivable_service
    MIGRATIONS_ADDRESS_SCHEMA: addresses


    BILLING_GALAXION_SCHEMA: billing_view

configuration:
  env:
    SPRING_PROFILES_ACTIVE: output-logs-as-json
