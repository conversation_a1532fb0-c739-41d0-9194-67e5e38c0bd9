apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: financial-documents
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
  sources:
  - chart: financial-documents
    helm:
      valueFiles:
      - $values/microservices/billing/financial-documents/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: "1.4.0-SNAPSHOT"
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
