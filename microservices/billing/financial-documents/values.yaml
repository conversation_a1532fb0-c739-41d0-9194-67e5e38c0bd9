main:
  useConfigMap: true
  replicas: 1
  securityContext: enabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/glx/itsf-team/financial-documents
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  aliveInitialDelaySeconds: 90
  alivePeriodSeconds: 90
  readyInitialDelaySeconds: 90
  readyPeriodSeconds: 90
  labels:
    owner: tsf
  resources:
    limits:
      cpu: "500m"
      memory: "2Gi"
    requests:
      cpu: "50m"
      memory: "1Gi"

dbmdl:
  resources:
    limits:
      cpu: "300m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  command: [ "sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$DATASOURCE_USERNAME --password=$DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update" ]
  labels:
    owner: itsf
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/financial-documents
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    GALAXION_USER_IDENTIFIER: SYSTEM
    GALAXION_USER_TYPE: SYSTEM

  envFrom:
  - secretRef:
      name: maria-database

configuration:
  envFrom:
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: keycloak-access
  envSecret:
    SPRING_DATASOURCE_USERNAME: maria-database/username
    SPRING_DATASOURCE_PASSWORD: maria-database/password
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/SPRING_RABBITMQ_USERNAME
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/SPRING_RABBITMQ_PASSWORD
  env:
    FEIGN_CLIENT_CONFIG_DEFAULT_LOGGERLEVEL: FULL
    ### HEADERS ###
    GALAXION_USER_IDENTIFIER: SYSTEM
    GALAXION_USER_TYPE: SYSTEM
    ### SERVER ###
    JAVA_TOOL_OPTIONS: "-Xms1024m -Xmx1024m -Djavax.net.debug=ssl:handshake:verbose"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/financial-documents
    ### RABBITMQ ###
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    ### SPRING ADMIN ###
    SPRING_BOOT_ADMIN_CLIENT_ENABLED: true
    SPRING_BOOT_ADMIN_CLIENT_URL: http://financial-documents:8080
    SPRING_BOOT_ADMIN_CLIENT_INSTANCE_SERVICE_BASE_URL: http://financial-documents:8080
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ENVIRONMENT_URL_ADJUSTMENTSERVICE: adjustments-service:8080
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_BILLINGSYSTEM: billing-api:8080
    ENVIRONMENT_URL_ACCOUNTRECEIVABLE_FACADE: account-receivable-facade:8080
    ### SFTP ###
    ENVIRONMENT_FTP_USERNAME: glx-sftp
    ENVIRONMENT_FTP_PASSWORD: KtBtbES1EEYZcyt
    ENVIRONMENT_FTP_HOST: ************
    ENVIRONMENT_FTP_DIR: app/Tigo/galaxion/f1/input
#    SPRINGDOC_SWAGGER_UI_PATH: /financial-documents/swagger-ui.html
#    SPRINGDOC_API_DOCS_PATH: /financial-documents/v3/api-docs
