main:
  useConfigMap: true
  replicas: 1
  securityContext: enabled
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/billing/billing-system-ui
    tag: 0.2.0
  labels:
    audience: itsffr
    netpol-vip-galaxionitsf-dev: "true"
    owner: tsf
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  noHealthProbes: true
    
configuration:
  env:
    API_URL: https://glx-billing-apigateway-pa-stg.tigo.cam
    SSE_URL: https://glx-billing-pa-stg.tigo.cam
    DISABLE_SSE: false

    KEYCLOAK_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_CLIENT: galaxion-billing-ui

    RESOURCE_TRANSLATIONS_MODE: default
    RESOURCE_THEMES_MODE: default
    RESOURCE_LOGO_MODE: default

    BRANDS: "TIGO"
    ACCOUNT_TYPES: "B2C"

    ### Logging ###
    KRAKEND_LOG_LEVEL: DEBUG
