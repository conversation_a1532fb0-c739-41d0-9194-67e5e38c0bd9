apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: billing-ui
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
  sources:
  - chart: billing-system-ui
    helm:
      valueFiles:
      - $values/microservices/billing/billing-ui/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    targetRevision: "0.2.0"
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
