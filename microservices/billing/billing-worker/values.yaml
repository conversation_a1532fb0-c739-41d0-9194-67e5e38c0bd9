global:
  name: billing-worker
  description: Billing Worker
  maintainers:
    - email: <EMAIL>
      name: infra
  labels:
    team: itsf

main:
  replicas: 1
  securityContext: enabled
  labels:
    owner: itsf
  annotations:
    traffic.sidecar.istio.io/excludeOutboundPorts: "443"
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/billing/billing-system/billing-worker
    tag: 0.3.0
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  resources:
    limits:
      cpu: "500m"
      memory: "4000Mi"
    requests:
      cpu: "50m"
      memory: "2000Mi"
  ## Only if worker plugin exist ###
  initContainers:
    - name: plugin-downloader
      #image: rtsp/net-tools
      volumeMounts:
       - name: worker-plugin-volume
         mountPath: /data/plugin/worker
      image: curlimages/curl
      # image: bitnami/minideb:bookworm
      #image: alpine:3.19
      securityContext:
        runAsUser: 0
        readOnlyRootFilesystem: false
      command:
        - sh
        - -c
        - |
          rm -rf /data/plugin/worker/*.jar
          mkdir -p /data/plugin/worker

          #  sleep 5000;
          echo "Download Plugin..." 
          RESPONSE=$(curl --connect-timeout 20 -sSL -k -X GET -H "HOST:${NEXUS_IP}" -G "https://${NEXUS_URL}/service/rest/v1/search/assets" \
          -H "Cache-Control: no-cache" \
          -d repository=maven-snapshots \
          -d maven.groupId=fr.njj \
          -d maven.artifactId=${PLUGIN_NAME} \
          -d maven.baseVersion=${PLUGIN_VERSION} \
          -d maven.extension=jar \
          -d maven.classifier=jar-with-dependencies
          -d sort=lastModified \
          -d direction=desc)
          
           DOWNLOAD_URL=$(echo "$RESPONSE" \
            | grep -E '"downloadUrl"|"lastModified"' \
            | paste - - \
            | sort -k2 -r \
            | head -n1 \
            | sed -E 's/.*"downloadUrl"[[:space:]]*:[[:space:]]*"([^"]+)".*/\1/')
        
          if [ -z "$DOWNLOAD_URL" ]; then
            echo "ERROR: No plugin download URL found!"
            cat /etc/resolv.conf
            exit 1
          fi
  
          echo "Download from: $DOWNLOAD_URL"
          #curl -fsSL -k "$DOWNLOAD_URL" -o /plugin/worker/worker-plugin.jar && echo "Plugin downloaded successfully." \
          curl -fsSL -k "$DOWNLOAD_URL" -o  /data/plugin/worker/worker-plugin.jar && echo "Plugin downloaded successfully." \
          || { echo "ERROR: Failed to download plugin!"; exit 1; }

      env:
        - name: NEXUS_IP
          value: "*************"
        - name: NEXUS_URL
          value: nexus-tsf.tigo.cam
        - name: PLUGIN_VERSION
          value: 1.2.0-SNAPSHOT
        - name: PLUGIN_NAME
          value: billing-system-plugin-worker
      volumeMounts:
        - name: worker-plugin-volume
          mountPath: /data

#dbmdl:
#  resources:
#    limits:
#      cpu: "1"
#      memory: "1024Mi"
#    requests:
#      cpu: "500m"
#      memory: "1024Mi"
#  command: [ "sh", "-c", "echo 'no dbmdl'" ]
#  labels:
#    owner: itsf


configuration:
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx2000m
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### DATABASE BILLING SYSTEM (POSTGRES) ###
    SPRING_DATASOURCE_USERNAME: postgres
    SPRING_DATASOURCE_PASSWORD: Soporte2025!
    SPRING_DATASOURCE_URL: *****************************************************
    ### DATABASE GALAXION GENERAL (MARIADB) ###
#    SPRING_LIQUIBASE_ENABLED: false
    SPRING_DATASOURCE_GALAXION_USERNAME: glx-pa
    SPRING_DATASOURCE_GALAXION_PASSWORD: Soporte2025!
    SPRING_DATASOURCE_GALAXION_URL: **************************,172.16.79.10:3306/billing_view
    ### DATABASE GALAXION USAGES (POSTGRES) (Only if BILLING_CONFIGURATION_CDRUSAGE_PROVIDER_TYPE is sql_view ###
    BILLING_CONFIGURATION_CDRUSAGE_PROVIDER_TYPE: sql_view
    SPRING_DATASOURCE_GALAXION_USAGE_USERNAME: postgres
    SPRING_DATASOURCE_GALAXION_USAGE_PASSWORD: Soporte2025!
    SPRING_DATASOURCE_GALAXION_USAGE_URL: ************************************************************
    ### RABBITMQ ###
    SPRING_RABBITMQ_USERNAME: "glxpa"
    SPRING_RABBITMQ_PASSWORD: "Soporte2025!"
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    SPRING_RABBITMQ_VIRTUAL_HOST: /pa
    ### PLUGIN ###
    ### Only if worker plugin exist ###
    BILLING_CONFIGURATION_WORKER_PLUGINS_DIRECTORY: /data/plugin/worker
    ### BILLING SYSTEM ###
    BILLING_CONFIGURATION_BATCH_PARTITIONSIZE: 1000
    BILLING_CONFIGURATION_BATCH_RETRYONWORKERRETRYEXCEPTIONSIZE: 3
    BILLING_CONFIGURATION_BATCH_CANCELCHUNKAFTERITEMSSKIPSIZE: 10
    # file-system or s3
    BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM: file-system
    # If file-system:
    BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_FILESYSTEM_DIRECTORY: /data/plugin/worker/invoices
    # If S3, de-comment:
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_ENDPOINT: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_REGION: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_ACCESS_KEY_ID: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_ACCESS_KEY_SECRET: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_BUCKET_NAME: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_TRUST_ALL_CERTIFICATES: true
    # Usage provider type : sql_view or webservice #
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ENVIRONMENT_URL_CDRUSAGESERVICE: cdr-repository-service:8080
    ### SFTP ###
    BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_FTP_USERNAME: glx-sftp
    BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_FTP_PASSWORD: KtBtbES1EEYZcyt
    BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_FTP_HOST: ************
    BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_FTP_DIR: app/Tigo/galaxion/f1/input
    BILLING_CONFIGURATION_INVOICE_TEMPORARYDIRECTORY: /tmp
    BILLING_CONFIGURATION_FEATURE_DETALLES_ENABLED: true

persistence:
  enabled: true
  claims:
## Only if worker plugin exist ###
    - name: worker-plugin-volume
      resourcePolicy: keep
      accessMode: ReadWriteMany
      storageClassName: storage-galaxion
      alreadyExist: true
      mountPath: /data
      size: 2Gi
### Only if env variable "BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM" is "file-system" ###
### (Already exist is true because created by api) ###
#    - name: data-invoice
#      alreadyExist: true
#      mountPath: /data/
