global:
  name: billing-api
  description: Billing API
  maintainers:
    - email: <EMAIL>
      name: infra
  labels:
    team: itsf

main:
  replicas: 1
  useConfigMap: true
  securityContext: enabled
  labels:
    owner: itsf
  annotations:
    traffic.sidecar.istio.io/excludeOutboundPorts: "443"
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/billing/billing-system/billing-api
    tag: 0.3.0
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  resources:
    limits:
      cpu: "500m"
      memory: "4000Mi"
    requests:
      cpu: "50m"
      memory: "2000Mi"
  ### Only if api plugin exist ###
  initContainers:
    - name: plugin-downloader
      volumeMounts:
       - name: billing-api-plugin-volume
         mountPath: /data/
      image: curlimages/curl
      securityContext:
        runAsUser: 0
        readOnlyRootFilesystem: false
      command:
        - sh
        - -c
        - |
          rm -rf /data/plugin/billing-api/*
          mkdir -p /data/plugin/billing-api

          echo "Download Plugin..." 
          RESPONSE=$(curl --connect-timeout 20 -sSL -k -X GET  -G "https://${NEXUS_URL}/service/rest/v1/search/assets" \
          -d repository=maven-snapshots \
          -d maven.groupId=fr.njj \
          -d maven.artifactId=${PLUGIN_NAME} \
          -d maven.baseVersion=${PLUGIN_VERSION} \
          -d maven.extension=jar \
          -d maven.classifier=jar-with-dependencies)
          
          DOWNLOAD_URL=$(echo "$RESPONSE" \
            | grep -E '"downloadUrl"|"lastModified"' \
            | paste - - \
            | sort -k2 -r \
            | head -n1 \
            | sed -E 's/.*"downloadUrl"[[:space:]]*:[[:space:]]*"([^"]+)".*/\1/')
        
          if [ -z "$DOWNLOAD_URL" ]; then
            echo "ERROR: No plugin download URL found!"
            cat /etc/resolv.conf
            exit 1
          fi
  
          echo "Download from: $DOWNLOAD_URL"
          curl -fsSL -k "$DOWNLOAD_URL" -o  /data/plugin/billing-api/billing-plugin.jar && echo "Plugin downloaded successfully." \
          || { echo "ERROR: Failed to download plugin!"; exit 1; }
      env:
        - name: NEXUS_URL
          value: nexus-tsf.tigo.cam
        - name: PLUGIN_VERSION
          value: 1.2.0-SNAPSHOT
        - name: PLUGIN_NAME
          value: billing-system-plugin-api
      #volumeMounts:
      #  - name: billing-api-plugin-volume
      #    mountPath: /data
dbmdl:
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  command: [ "sh", "-c", "docker-entrypoint.sh --url=$BILLING_DATASOURCE_PERSISTENCE_URL --username=$BILLING_DATASOURCE_PERSISTENCE_USERNAME --password=$BILLING_DATASOURCE_PERSISTENCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$BILLING_DATASOURCE_PERSISTENCE_CHANGELOGFILE update" ]
  labels:
    owner: itsf
  env:
    # PostgreSQL
    BILLING_DATASOURCE_PERSISTENCE_USERNAME: postgres
    BILLING_DATASOURCE_PERSISTENCE_PASSWORD: Soporte2025!
    BILLING_DATASOURCE_PERSISTENCE_URL: *****************************************************
    BILLING_DATASOURCE_PERSISTENCE_CHANGELOGFILE: db/changelog/db.billing-system.changelog-master.yaml

configuration:
  envFrom:
    - secretRef:
       name: rabbitmq-creds
  envSecret:
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/SPRING_RABBITMQ_USERNAME
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/SPRING_RABBITMQ_PASSWORD

  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx2000m
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### DATABASE BILLING SYSTEM (POSTGRES) ###
    SPRING_DATASOURCE_USERNAME: postgres
    SPRING_DATASOURCE_PASSWORD: Soporte2025!
    SPRING_DATASOURCE_URL: *****************************************************
    ### DATABASE GALAXION (MARIADB) ###
    SPRING_DATASOURCE_GALAXION_USERNAME: glx-pa
    SPRING_DATASOURCE_GALAXION_PASSWORD: Soporte2025!
    SPRING_DATASOURCE_GALAXION_URL: **************************,************:3306/billing_view
    ### RABBITMQ ###
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    SPRING_RABBITMQ_VIRTUAL_HOST: /pa
    ### PLUGIN ###
    ### Only if api plugin exist ###
    BILLING_CONFIGURATION_API_PLUGINS_DIRECTORY: /data/plugin/billing-api
    ### BILLING SYSTEM ###
    BILLING_CONFIGURATION_BATCH_PARTITIONSIZE: 1000
    BILLING_CONFIGURATION_BATCH_PRESELECTIONSIZE: 10000
    BILLING_CONFIGURATION_BATCH_PRESELECTION_THREAD_MAX: 5
    # file-system or s3
    BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM: file-system
    # If S3, de-comment:
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_ENDPOINT: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_REGION: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_ACCESS_KEY_ID: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_ACCESS_KEY_SECRET: TO_BE_DEFINED
    # BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM_S3_CLIENT_TRUST_ALL_CERTIFICATES: true


#persistence:
#  enabled: true
#  claims:
### Only if api plugin exist ###
#    - name: api-plugin-volume
#      resourcePolicy: keep
#      accessMode: ReadWriteMany
#      size: 1Gi
#      storageClassName: TO_BE_DEFINED
#      alreadyExist: false
#      mountPath: /data/plugin/api
### Only if env variable "BILLING_CONFIGURATION_INVOICE_UPLOADSYSTEM" is "file-system" ###
#    - name: data-invoice
#      resourcePolicy: keep
#      accessMode: ReadWriteMany
#      size: 1Gi
#      storageClassName: TO_BE_DEFINED
#      alreadyExist: false
#      mountPath: /data/
persistence:
  enabled: true
  claims:
    - name: billing-api-plugin-volume
      resourcePolicy: keep
      accessMode: ReadWriteMany
      storageClassName: storage-galaxion
      alreadyExist: true
      mountPath: /data
      size: 2Gi
