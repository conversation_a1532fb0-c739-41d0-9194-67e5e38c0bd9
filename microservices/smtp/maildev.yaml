apiVersion: apps/v1
kind: Deployment
metadata:
  name: maildev
  namespace: glx-pa
  annotations:
    traffic.sidecar.istio.io/excludeOutboundPorts: "1025"  # Evita que Istio intercepte SMTP
spec:
  replicas: 1
  selector:
    matchLabels:
      app: maildev
  template:
    metadata:
      labels:
        app: maildev
    spec:
      containers:
        - name: maildev
          image: maildev/maildev
          ports:
            - containerPort: 1025
            - containerPort: 1080
          env:
            - name: MAILDEV_SMTP_PORT
              value: "1025"
            - name: MAILDEV_WEB_PORT
              value: "1080"
            - name: MAILDEV_INCOMING_USER
              value: "admin"
            - name: MAILDEV_INCOMING_PASS
              value: "admin"
---
apiVersion: v1
kind: Service
metadata:
  name: maildev
  namespace: glx-pa
spec:
  selector:
    app: maildev
  ports:
    - name: smtp
      protocol: TCP
      port: 1025
      targetPort: 1025
    - name: web
      protocol: TCP
      port: 1080
      targetPort: 1080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: maildev
  namespace: glx-pa
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-headers: Content-Type, authorization, x-b3-traceid, x-b3-spanid, x-b3-parentspanid, x-b3-sampled, x-b3-flags, x-ot-span-context
    nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS, PATCH
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: glx-crm-pa-stg.tigo.cam
    http:
      paths:
      - backend:
          service:
            name: maildev
            port:
              number: 1080
        path: /maildev(/|$)(.*)
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - glx-crm-pa-stg.tigo.cam
    secretName: wildcard-galaxion-tls