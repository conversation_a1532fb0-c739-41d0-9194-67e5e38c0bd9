configuration:
  env:
    J<PERSON><PERSON>_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SPRING_PROFILES_ACTIVE: keycloak-monotenant
    KEYCLOAK_REALMS_RESOURCE_0: keycloak-access/KEYCLOAK_RESOURCE
    KEYCLOAK_REALMS_SECRET_0: '0123456789'
    SPRING_DATASOURCE_URL: **************************,************:3306/collections
    UI_KEYCLOAK_AUTHSERVERURL: keycloak-access/KEYCLOAK_AUTH_SERVER_URL
    UI_KEYCLOAK_JAVASCRIPTADAPTER: https://glx-iam-pa-stg.tigo.cam/auth/js/keycloak.min.js
    UI_KEYCLOAK_REALMSELECTORLIST: keycloak-access/KEYCLOAK_REALM
    UI_KEYCLOAK_RESOURCE: galaxion-collections-ui
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access

dbmdl:
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/collections
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  envFrom:
    - secretRef:
        name: maria-database
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/collections-service
    tag: 7.2.20
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

main:
  useConfigMap: true
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/collections-service
    tag: 7.2.20
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"