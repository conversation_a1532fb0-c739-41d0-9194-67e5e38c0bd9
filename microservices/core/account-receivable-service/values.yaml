global:
  name: account-receivable-service
  description: Account Receivable Service
  maintainers:
  - email: victor.blan<PERSON>@itsfactory.fr
    name: <PERSON>
  labels:
    team: itsf

dbmdl:
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  env:
    LIQUIBASE_DATASOURCE_URL: jdbc:mariadb://***********,************:3306/account_receivable_service
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  envFrom:
  - secretRef:
      name: maria-database

main:
  useConfigMap: false
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/account-receivable-service
    tag: 3.1.0
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"

configuration:
  envFrom:
  - secretRef:
      name: maria-database
  env:
    ### SERVER ###
    SERVER_PORT: 8080
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    ### LOGS ###
    AR_POLLER_LOGLEVEL: WARN
    SERVICE_LOG_LEVEL: WARN
    POLLER_LOG_LEVEL: WARN
    ### DATABASE ###
    SPRING_DATASOURCE_USERNAME: maria-database/username
    SPRING_DATASOURCE_PASSWORD: maria-database/password
    SPRING_DATASOURCE_URL: jdbc:mariadb://***********,************:3306/account_receivable_service
    DATABASE_SERVICE_HOSTNAME: "***********"
    DATABASE_SERVICE_PORT: "3306"
    DATABASE_SERVICE_NAME: account_receivable_service
    DATABASE_SERVICE_POOL_MINSIZE: "10"
    DATABASE_SERVICE_POOL_MAXACTIVE: "300"
    ### WEBSERVICES URLS ###
    MOBILE_COLLECTION_ENDPOINT: collections-service:8080
    MOBILE_CRM_ENDPOINT: accounts-service:8080
    ### SPECIFIC PROJECT VALUES ###
    AR_SERVICE_REQUESTOR_ID: billing-test
    AR_POLLER_REQUESTOR_ID: billing-test
    ACCOUNT_RECEIVABLE_TIMEZONE: Europe/Dublin
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
