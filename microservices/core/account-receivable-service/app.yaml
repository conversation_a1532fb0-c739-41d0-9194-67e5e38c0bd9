apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: account-receivable-service
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: account-receivable-service
    helm:
      valueFiles:
      - $values/microservices/core/account-receivable-service/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    targetRevision: 3.1.0
    #targetRevision: 2.0.2 we need to try
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
