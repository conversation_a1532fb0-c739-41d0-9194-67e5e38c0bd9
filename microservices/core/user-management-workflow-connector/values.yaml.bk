global:
  name: user-management-workflow-connector
  description: User Management Workflow Connector
  maintainers:
    - email: j<PERSON><PERSON>.be<PERSON><PERSON>@risf.io
      name: <PERSON>
    - email: <EMAIL>
      name: <PERSON><PERSON><PERSON>y
  labels:
    team: risf

configuration:
  envSecret:
    SPRING_RABBITMQ_USERNAME: "rabbitmq-creds/SPRING_RABBITMQ_USERNAME"
    SPRING_RABBITMQ_PASSWORD: "rabbitmq-creds/SPRING_RABBITMQ_PASSWORD"

  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### RABBITMQ ###
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    ### KEYCLOAK ###
    ### KEYCLOAK ###
    KEY<PERSON><PERSON><PERSON>_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_SSL_REQUIRED: external
    KEYCLOAK_REALM: galaxion-workflow-connectors
    KEYCLOAK_RESOURCE: galaxion-workflow-connectors
    KEYCLOAK_CREDENTIALS_SECRET: '**********'
    ### WEBSERVICES URLS ###
    WEBSERVICE_USER_MANAGEMENT_URL: users-service:8080
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/user-management-workflow-connector
    tag: 2.0.1
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: risf
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
