configuration:
  env:
    JAV<PERSON>_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SPRING_PROFILES_ACTIVE: keycloak-monotenant
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-search-engine
    KEYCLOAK_REALMS_SECRET_0: '**********'
    SPRING_DATASOURCE_URL: **************************,************:3306/search_engine
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080

  envFrom:
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access

dbmdl:
  image:
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/search-engine-service
    tag: 6.2.2
  env:
    MIGRATIONS_ACCOUNT_SCHEMA: accounts
    MIGRATIONS_ADDRESS_SCHEMA: addresses
    MIGRATIONS_CONTACT_SCHEMA: contacts
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/search_engine
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update -Daccount.schema=$MIGRATIONS_ACCOUNT_SCHEMA"]
  labels:
    owner: itsf

  envFrom:
  - secretRef:
      name: maria-database
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "100m"
      memory: "256Mi"

main:
  useConfigMap: true
  imagePullSecret: nexus-local
  securityContext: disabled
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/search-engine-service
    tag: 6.2.2
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"