configuration:
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/accounts?sessionVariables=wsrep_sync_wait=7
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACCOUNTRECEIVABLEFACADE: account-receivable-facade:8080
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_ADDRESSESSERVICE: addresses-service:8080
    ENVIRONMENT_URL_BILLINGCYCLESSERVICE: billing-cycles-service:8080
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_PAYMENTSSERVICE: payments-service:8080
    ENVIRONMENT_URL_TOPUPSSERVICE: top-up-service:8080
    ### SPECIFIC PROJECT VALUES ###
    ADJUSTMENT_DEFAULTLOCALE_COUNTRY: 'PA' # Country for the language to display properly amount to CSR agent through exception message. An ISO 3166 alpha-2 country code or a UN M.49 numeric-3 area code.
    ADJUSTMENT_DEFAULTLOCALE_LANGUAGE: 'en' # Language to display properly amount to CSR agent through exception message. An ISO 639 alpha-2 or alpha-3 language code, or a language subtag up to 8 characters in length.
    TIMETOLIVE_CACHE_CATALOGSERVICE: '1800000' # Time to live in milliseconds for catalog cache.
    ### S3 STORAGE ###
    # S3_CLIENT_ENDPOINT: OPTIONAL
    # S3_CLIENT_REGION: OPTIONAL
    # S3_CLIENT_ACCESS_KEY_ID: OPTIONAL
    # S3_CLIENT_ACCESS_KEY_SECRET: OPTIONAL
    # S3_BUCKET_NAME: OPTIONAL
    # S3_CLIENT_TRUST_ALL_CERTIFICATES: OPTIONAL
    ### ENABLE ADJUSTMENT NOTE FUNCTIONALITY ###
    ADJUSTMENT_NOTE_ENABLED: false
    REPRINT_PAPER_ADJUSTMENT_NOTE_CHARGE_CATALOG_CODE: ADJUSTMENT_NOTE_REPRINT_CHARGE

dbmdl:
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/adjustments-service
    tag: 7.2.5
  resources:
    limits:
      cpu: "1"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
  labels:
    owner: tigo
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  envFrom:
  - secretRef:
      name: maria-database
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/accounts?sessionVariables=wsrep_sync_wait=7
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml

main:
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/adjustments-service
    tag: 7.2.5
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
  aliveInitialDelaySeconds: 60
  alivePeriodSeconds: 60
  readyInitialDelaySeconds: 150
  readyPeriodSeconds: 60
  alivePath: /actuator/health
  readyPath: /actuator/health