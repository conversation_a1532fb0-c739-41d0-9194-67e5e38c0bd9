apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: billing-cycles-service
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - chart: billing-cycles-service
      helm:
        valueFiles:
          - $values/microservices/core/billing-cycles-service/values.yaml
      ref: helm
      repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
      targetRevision: 3.1.2
    - ref: values
      repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
      targetRevision: stg
