global:
  name: billing-cycles-service
  description: Billing cycles Service
  maintainers:
    - email: <EMAIL>
      name: <PERSON>
  labels:
    team: itsf
main:
  replicas: 1
  useConfigMap: true
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/billing-cycles-service
    tag: 3.1.2
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"


dbmdl:
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "1024Mi"
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  labels:
    owner: itsf
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,***********0:3306/billing_cycles
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  envFrom:
  - secretRef:
      name: maria-database

configuration:
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - configMapRef:
      name: keycloak-access

  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,***********0:3306/billing_cycles
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    ### WEBSERVICES URLS ####
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_ACCOUNTRECEIVABLEFACADE: account-receivable-facade:8080