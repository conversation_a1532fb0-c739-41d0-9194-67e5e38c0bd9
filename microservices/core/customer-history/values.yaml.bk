global:
  name: customer-history-service
  description: Customer History Service
  maintainers:
  - email: <EMAIL>
    name: <PERSON><PERSON>AUDRON
  labels:
    team: monacotelecom

# persistence:
#   enabled: true
#   claims:
#     - name: data-customer-history-service
#       resourcePolicy: keep
#       accessMode: ReadWriteMany
#       size: 1Gi
#       storageClassName: silver-mcc
#       alreadyExist: false
#       mountPath: /data

dbmdl:
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/customer-history
    tag: 2.5.4

  envFrom:
  - secretRef:
      name: maria-database
  env:
    SPRING_DATASOURCE_URL: jdbc:mariadb://***********,************:3306/customer_history
    SPRING_LIQUIBASE_PARAMETERS_HISTORYSCHEMA: customer_history
    SPRING_LIQUIBASE_PARAMETERS_ACCOUNTSCHEMA: accounts

main:
  useConfigMap: true
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/customer-history
    tag: 2.5.4
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  aliveInitialDelaySeconds: 280
  alivePeriodSeconds: 60
  readyInitialDelaySeconds: 280
  readyPeriodSeconds: 60
  resources:
    limits:
      cpu: "500m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

configuration:
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - configMapRef:
      name: keycloak-access
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m -Djavax.net.ssl.trustStore=/java/cacerts -Djava.security.properties=/java/custom.security/custom.security
    SERVER_PORT: 8080
    SPRING_PROFILES_ACTIVE: output-logs-as-json,keycloak-multitenant
    ### RABBITMQ ###
    CUSTOMER_HISTORY_RABBITMQ_DESTINATION: customer-history
    CUSTOMER_HISTORY_RABBITMQ_GROUP: send.customer.history.event
    SPRING_CLOUD_STREAM_RABBIT_BINDINGS_CUSTOMERHISTORYADDHISTORY_CONSUMER_DEADLETTERQUEUENAME: dead.letter.send.customer.history.event
    SPRING_CLOUD_STREAM_RABBIT_BINDINGS_CUSTOMERHISTORYADDHISTORY_CONSUMER_DEADLETTEREXCHANGE: customerhistory-deadletter-exchange
    SPRING_CLOUD_STREAM_RABBIT_BINDINGS_CUSTOMERHISTORYADDHISTORY_CONSUMER_DEADLETTERROUTINGKEY: dead.letter.customer-history
    SPRING_CLOUD_STREAM_RABBIT_BINDINGS_CUSTOMERHISTORYADDHISTORY_CONSUMER_BINDINGROUTINGKEY: send.customer.history.event
    SPRING_CLOUD_STREAM_RABBIT_BINDINGS_CUSTOMERHISTORYADDHISTORY_CONSUMER_AUTOBINDLQ: "true"
    SPRING_CLOUD_STREAM_RABBIT_BINDINGS_CUSTOMERHISTORYADDHISTORY_CONSUMER_REPUBLISHTODLQ: "true"
    SPRING_CLOUD_STREAM_RABBIT_BINDINGS_CUSTOMERHISTORYADDHISTORY_CONSUMER_BINDQUEUE: "true"
    ### DATABASE ###
    SPRING_DATASOURCE_URL: jdbc:mariadb://***********,************:3306/customer_history
    CUSTOMER_HISTORY_DDL_AUTO: none
    ### KEYCLOAK ###
    KEYCLOAK_RESOURCE: galaxion-customer-history
    KEYCLOAK_CREDENTIALS_SECRET: '**********'
    KEYCLOAK_1_RESOURCE: galaxion-customer-history
    KEYCLOAK_1_CREDENTIALS_SECRET: '**********'
