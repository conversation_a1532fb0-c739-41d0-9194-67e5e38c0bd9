apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: add-ons-service
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: add-ons-service
    helm:
      valueFiles:
      - $values/microservices/core/add-ons-service/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    targetRevision: 5.4.0
  - ref: values
    repoURL: "https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git"
    targetRevision: stg
