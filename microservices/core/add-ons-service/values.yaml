
global:
  name: add-ons-service
  description: Add ons Service
  maintainers:
    - email: <EMAIL>
      name: <PERSON>
  labels:
    team: itsf
main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/add-ons-service
    tag: 5.4.0
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
dbmdl:
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  image:
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/add-ons-service
    tag: 5.4.0
  command:
    [
      "sh",
      "-c",
      "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update",
    ]
  labels:
    owner: itsf
  envFrom:
  - secretRef:
      name: maria-database
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/addons
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXT: eir
configuration:
  envFrom:
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: keycloak-multitenant,output-logs-as-json
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/addons
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    SPRING_RABBITMQ_VIRTUAL_HOST: /pa
    ### KEYCLOAK ###
    KEYCLOAK_REALMS_REALM_NAME_0: keycloak-access/KEYCLOAK_REALM
    KEYCLOAK_REALMS_AUTH_SERVER_URL_0: keycloak-access/KEYCLOAK_AUTH_SERVER_URL
    KEYCLOAK_REALMS_SSL_REQUIRED_0: external
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-addons
    KEYCLOAK_REALMS_SECRET_0: "**********"
    KEYCLOAK_REALMS_PRINCIPAL_ATTRIBUTE_0: preferred_username
    ### CLAIMS CHANNEL KEY ###
    KEYCLOAKOTHERCLAIMS.CHANNELS.KEY: "salesChannels"
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ENVIRONMENT_URL_COLLECTIONSSERVICE: collections-service:8080
    ENVIRONMENT_URL_WORKFLOWQUERYFACADE: workflow-query-facade:8080
    ### SPECIFICS VALUES ####
    #ADDON_VALIDATION_SERVICES_USECASES_IPTV: TERMINATION #The list of workflow use cases to check before
    #ADDON_VALIDATION_SERVICES_USECASES_FIBER: TERMINATION #The list of workflow use cases to check before
    #ADDON_VALIDATION_SERVICES_USECASES_DSL: TERMINATION #The list of workflow use cases to check before
    #ADDON_VALIDATION_SERVICES_USECASES_VOIP: TERMINATION,CHANGE_VOBB_NUMBER,PORT_IN #The list of workflow use cases to check before
    #ADDON_VALIDATION_PARENTSUBSCRIPTION_SERVICEDOMAINSTOCHECK_IPTV: FIBER, DSL #The list of service domain to check pending order on the parent subscription. For example if we manage add-on on a TV service we have to check only the FIBER and DSL services of the parent subscription, we don't need to check the VOIP one.
    FEIGN_CLIENT_CONFIG_DEFAULT_LOGGERLEVEL: FULL
