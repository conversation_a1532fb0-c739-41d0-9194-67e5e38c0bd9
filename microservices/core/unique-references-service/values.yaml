configuration:
  env:
    SPRING_DATASOURCE_URL: **************************,172.16.79.10:3306/unique_references
    HASHIDS_MINHASHLENGTH: 10
  envFrom:
  - secretRef:
      name: maria-database

dbmdl:
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,172.16.79.10:3306/unique_references
  envFrom:
  - secretRef:
      name: maria-database
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/unique-references-service
    tag: 4.0.0
  resources:
    limits:
      cpu: "100m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "512Mi"

main:
  useConfigMap: true
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/unique-references-service
    tag: 4.0.0
  resources:
    limits:
      cpu: "200m"
      memory: "1024Mi"
    requests:
      cpu: "100m"
      memory: "256Mi"
