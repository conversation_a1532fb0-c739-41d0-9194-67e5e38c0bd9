global:
  name: billing-service
  description: Billing Service

configuration:
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### DATABASE ###
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_ADDRESSESSERVICE: addresses-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_BILLINGARCHIVESERVICE: billing-archive-service:8080/billing-archive-service
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ### OTHERS ###
    RESEND_PAPER_INVOICE_CHARGE_CATALOG_CODE: TO_BE_DEFINED
    BILLINGARCHIVESERVICE_REQUESTORID: 'my-account-user' # Requestor id that will be sent in requestor-id header while calling billing-archive-service
    # S3_CLIENT_ENDPOINT: TO_BE_DEFINED
    # S3_CLIENT_REGION: TO_BE_DEFINED
    # S3_CLIENT_ACCESS_KEY: TO_BE_DEFINED
    # S3_CLIENT_ACCESS_KEY_SECRET: TO_BE_DEFINED
    # S3_BUCKET_NAME: TO_BE_DEFINED
    # S3_CLIENT_TRUST_ALL_CERTIFICATES: TO_BE_DEFINED
    ### BRITEBILL ###
    EIR_BRITEBILL_ENABLED: TO_BE_DEFINED # Enable or disable the Britebill feature : true/false
    EIR_BRITEBILL_MININVOICENUMBER: TO_BE_DEFINED # The minimum (inclusive) invoice number to consider the invoice as a Britebill invoice
    EIR_BRITEBILL_MAXINVOICENUMBER: TO_BE_DEFINED # The maximum (inclusive) invoice number to consider the invoice as a Britebill invoice
    EIR_BRITEBILL_SERVICE_HEADERS_AUTHORIZATION: TO_BE_DEFINED # The Authorization header used to authorize call to the Britebill application (Basic Auth)
    ENVIRONMENT_URL_EIRBRITEBILLSERVICE: TO_BE_DEFINED # The address of the Britebill application
    ### INVOICE RESEND ###
    INVOICE_RESEND_ENABLED: TO_BE_DEFINED

main:
  replicas: 1
  useConfigMap: true
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/billing-service
    tag: 2.1.1
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"



