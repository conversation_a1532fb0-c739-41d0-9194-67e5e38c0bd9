apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: workflow-query-facade
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: workflow-query-facade
    helm:
      valueFiles:
      - $values/microservices/core/workflow-query-facade/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
#    targetRevision: 1.4.3
    targetRevision: 1.7.4
  - ref: values
    repoURL: "https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git"
    targetRevision: stg
