configuration:
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    FEIGN_CLIENT_CONFIG_DEFAULT_LOGGERLEVEL: FULL
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: keycloak-monotenant
    ### KEYCLOAK ###
    KEYCLOAK_REALMS_REALM_NAME_0: galaxion
    KEYCLOAK_REALMS_AUTH_SERVER_URL_0: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALMS_SSL_REQUIRED_0: external
    KEYCLOAK_REALMS_PRINCIPAL_ATTRIBUTE_0: preferred_username
    ### KEYCLOAK ###
    KEYCLOAK_REALMS_RESOURCE_0: workflow-query
    KE<PERSON><PERSON>OAK_REALMS_SECRET_0: "**********"
    SECURITY_KEYCLOAK_REALM: galaxion
    SECURITY_KEYCLOAK_RESOURCE: workflow-query
    SECURITY_KEYCLOAK_SECRET: "**********"
    SECURITY_KEYCLOAK_URL: https://glx-iam-pa-stg.tigo.cam/auth
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_WORKFLOWQUERYSERVICE: workflow-query:8080
    # VARIABLES DE PARAMETRIZACION CANCEL ORDER
    cancel.orders.permitted.usecases: CUSTOMER_ACQUISITION
    cancel.orders.provisioning.sp.toCheck: SP_WAIT_BB_PROV_REQUEST
    environment.url.appointments-service: appointments-service:8080
    
  envFrom:
    - configMapRef:
        name: rabbitmq
    - secretRef:
        name: rabbitmq-creds
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/glx/glx-pa/orchestration-tools/workflow-engine/workflow-query-facade
    tag: 1.9.0-TSFD-10710
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "50m"
      memory: "256Mi"