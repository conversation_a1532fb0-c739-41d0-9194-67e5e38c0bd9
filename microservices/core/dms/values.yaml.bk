 global:
   name: documents-service
   description: Document Service
   maintainers:
   - email: <EMAIL>
     name: <PERSON><PERSON>DRON
   labels:
     team: monacotelecom

 persistence:
   enabled: true
   claims:
   - name: data-sit-dms
     resourcePolicy: keep
     accessMode: ReadWriteMany
     size: 2Gi
     storageClassName: galaxion-storage
     alreadyExist: true
     mountPath: /var/dms

 dbmdl:
   resources:
     limits:
       cpu: "100m"
       memory: "512Mi"
     requests:
       cpu: "100m"
       memory: "256Mi"
   image:
     pullPolicy: IfNotPresent
     repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/dms
     tag: 3.4.4
   command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
   envFrom:
   - secretRef:
       name: maria-database
   env:
     DMS_DB_URL: jdbc:mysql://***********,************:3306/documents
     SPRING_DATASOURCE_URL: **************************,************:3306/documents

 main:
   useConfigMap: true
   replicas: 1
   securityContext: disabled
   noHealthProbes: true
   image:
     pullPolicy: IfNotPresent
     repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/dms
     tag: 3.4.4
   port: 8080
   alivePath: /actuator/health
   readyPath: /actuator/health
   resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

 configuration:
   envFrom:
   - secretRef:
       name: rabbitmq-creds
   - configMapRef:
       name: rabbitmq
   - secretRef:
       name: maria-database
   - configMapRef:
       name: keycloak-access
   env:
#     ### SERVER ###
     JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
     SERVER_PORT: 8080
     SPRING_PROFILES_ACTIVE: keycloak-monotenant

#     ### DATABASE ###
     DMS_DDL_AUTO: none
     SPRING_DATASOURCE_DRIVER_CLASS_NAME: org.mariadb.jdbc.Driver
     SPRING_DATASOURCE_URL: **************************,************:3306/documents
     SPRING_DATASOURCE_HIKARI_MINIMUMIDLE: '3'
     SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT: 30000
     SPRING_DATASOURCE_HIKARI_IDLE_TIMEOUT: 600000
     SPRING_DATASOURCE_HIKARI_MAXLIFETIME: "1800000"
    ### KEYCLOAK ###
     KEYCLOAK_RESOURCE: galaxion
     KEYCLOAK_CREDENTIALS_SECRET: '**********'
     ### OTHERS ###
     FILE_DIRECTORY: /var/dms/files/
     FILE_SHAREDDIRECTORY: null
     ENVIRONMENT_CUSTOMERHISTORY_ISHISTORIZATIONACTIVE: true
     SPRING_CLOUD_STREAM_BINDINGS_CUSTOMERHISTORYADDHISTORY_DESTINATION: customer-history
     SPRING_CLOUD_STREAM_BINDINGS_CUSTOMERHISTORYADDHISTORY_PRODUCER_BINDINGROUTINGKEY: customer-history.send.customer.history.event
