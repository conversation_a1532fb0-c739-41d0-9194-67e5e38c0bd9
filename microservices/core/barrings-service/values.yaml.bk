global:
  name: barrings-service
  description: Barrings Service
  maintainers:
    - email: jero<PERSON>.be<PERSON><PERSON>@risf.io
      name: <PERSON>
    - email: <EMAIL>
      name: <PERSON><PERSON><PERSON> Cuny
  labels:
    team: risf
#virtualservice:
#  gateways:
#  - galaxion-eir-int-gateway
#  hosts:
#  - "barrings-service.eir-int.itsf.io"
main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/barrings-service
    tag: 4.3.2
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: risf
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
dbmdl:
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "1024Mi"
  image:
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/barrings-service
    tag: 4.3.2
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE --contexts=$LIQUIBASE_CONTEXTS update"]
  labels:
    owner: risf
  envFrom:
  - secretRef:
      name: maria-database
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/barrings
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXTS: eir

configuration:
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### DATASOURCE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/barrings
    SPRING_DATASOURCE_USERNAME: maria-database/username
    SPRING_DATASOURCE_PASSWORD: maria-database/password
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password
    ### HIKARI ###
    SPRING_DATASOURCE_HIKARI_MAXIMUMPOOLSIZE: 4
    SPRING_DATASOURCE_HIKARI_CONNECTIONTIMEOUT: 30000
    SPRING_DATASOURCE_HIKARI_MAXLIFETIME: 59000
    SPRING_DATASOURCE_HIKARI_IDLETIMEOUT: 59000
    SPRING_DATASOURCE_HIKARI_CONNECTIONINITSQL: SET SESSION wait_timeout=60
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
  envFrom:
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
