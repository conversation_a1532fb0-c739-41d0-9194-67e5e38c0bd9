configuration:
  envFrom:
  - configMapRef:
      name: keycloak-access
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
  env:
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: "8080"
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    SPRING_PROFILES_ACTIVE: keycloak-multitenant,output-logs-as-json
    SPRING_DATASOURCE_USERNAME: maria-database/username
    SPRING_DATASOURCE_PASSWORD: maria-database/password
    SPRING_DATASOURCE_URL: **************************,************:3306/account_receivable_service
    KEYCLOAK_REALMS_AUTH_SERVER_URL_0: keycloak-access/KEYCLOAK_AUTH_SERVER_URL
    KEYCLOAK_REALMS_REALM_NAME_0: keycloak-access/KEYCLOAK_REALM
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-accounts
    KEYCLOAK_REALMS_RESOURCE_1: galaxion-accounts
    KEYCLOAK_REALMS_SECRET_0: '**********'
    KEYCLOAK_REALMS_SECRET_1: '**********'
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password
    ENVIRONMENT_URL_ACCOUNTRECEIVABLESERVICE: account-receivable-service:8080
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080

main:
  useConfigMap: false
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/account-receivable-facade
    tag: 6.2.1
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
  aliveInitialDelaySeconds: 20
  alivePeriodSeconds: 20
  readyInitialDelaySeconds: 20
  readyPeriodSeconds: 20

  # autoscaling: {}
  #   # minReplicas: 2
  #   # maxReplicas: 5
  #   # targetCPUUtilizationPercentage: 70
  #   # targetMemoryUtilizationPercentage: 80