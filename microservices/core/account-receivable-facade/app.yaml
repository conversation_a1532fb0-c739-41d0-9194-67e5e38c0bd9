apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: account-receivable-facade
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: account-receivable-facade
    helm:
      valueFiles:
      - $values/microservices/core/account-receivable-facade/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    #targetRevision: 5.5.0
    targetRevision: 6.2.1 #we need this one
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
