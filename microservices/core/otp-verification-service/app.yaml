apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: otp-verification-service
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: otp-verification-service
    helm:
      valueFiles:
      - $values/microservices/core/otp-verification-service/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    targetRevision: 5.2.2
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
