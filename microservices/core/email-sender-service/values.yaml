configuration:
  env:    
    SPRING_MAIL_BULK_HOST: *************
    SPRING_MAIL_BULK_PORT: 25    
    SPRING_MAIL_STANDARD_HOST: *************
    SPRING_MAIL_STANDARD_PORT: 25
    # SPRING_MAIL_STANDARD_USERNAME: admin
    # SPRING_MAIL_BULK_PASSWORD: admin
    # SPRING_MAIL_STANDARD_PASSWORD: admin
    # SPRING_MAIL_BULK_USERNAME: admin    
    SPRING_RABBITMQ_HOST: ************
    SPRING_RABBITMQ_PORT: 5672
    SPRING_RABBITMQ_VIRTUAL_HOST: /pa
    LOGGING_LEVEL_FR_NJJ_GALAXION: DEBUG
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  envSecret:
    # SPRING_MAIL_STANDARD_USERNAME: rabbitmq-creds/rabbitmq-username
    # SPRING_MAIL_STANDARD_PASSWORD: rabbitmq-creds/rabbitmq-password
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password


main:
  useConfigMap: true
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/email-sender-service
    tag: 3.1.0
  
