apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: email-sender-service
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: email-sender-service
    helm:
      valueFiles:
      - $values/microservices/core/email-sender-service/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    #targetRevision: 3.1.0
    #repoURL: https://nexus.itsf.io/repository/helm-charts/
    targetRevision: 3.1.0
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
