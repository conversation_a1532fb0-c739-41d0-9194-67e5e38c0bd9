global:
  name: users-service
  description: User Service
  maintainers:
    - email: gera<PERSON>@itsfactory.fr
      name: <PERSON><PERSON><PERSON>
  labels:
    team: itsf

configuration:
  envFrom:
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/users
    SPRING_DATASOURCE_KEYCLOAK_URL: **************************,************:3306/users_key
    SPRING_DATASOURCE_KEYCLOAK_HIKARI_MAXIMUMPOOLSIZE: "10000"
    SPRING_DATASOURCE_KEYCLOAK_HIKARI_MINIMUMIDLE: "10000"
    SPRING_DATASOURCE_KEYCLOAK_HIKARI_MAXLIFETIME: "1000"
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    ### KEYCLOAK API ###
    ENVIRONMENT_KEYCLOAK_API_0_AUTHSERVERURL: https://glx-iam-pa-stg.tigo.cam/auth
    ENVIRONMENT_KEYCLOAK_API_0_CLIENT: galaxion
    ENVIRONMENT_KEYCLOAK_API_0_CLIENTSECRET: '**********'
    ENVIRONMENT_KEYCLOAK_API_0_REALM: galaxion-users
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_OTPVERIFICATIONSERVICE: otp-verification-service:8080
    DEEP_LINK_URL: TO_BE_DEFINED
    ### CHANGE EMAIL ###
    CHANGE_EMAIL_EXPIRATION_MINUTES: 5
    ## GOOGLE RECAPTCHA
    GOOGLE_RECAPTCHA_SECRET: '*****************'
    GOOGLE_RECAPTCHA_VERIFY_URL: 'https://www.google.com/recaptcha/api/siteverify'

dbmdl:
  image:
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/users-service
    tag: 7.1.6
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/users
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  labels:
    owner: itsf
  envFrom:
  - secretRef:
      name: maria-database
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "1024Mi"  

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/users-service
    tag: 7.1.6
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"