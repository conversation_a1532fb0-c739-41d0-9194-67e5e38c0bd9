global:
  name: customer-history-ui
  description: Customer history UI
  maintainers:
  - email: <EMAIL>
    name: <PERSON><PERSON> CHAUDRON
  labels:
    team: itsf

main:
  useConfigMap: true
  replicas: 1
  securityContext: enabled
  noHealthProbes: true

  image:
      pullPolicy: IfNotPresent
      repository: nexus-tsf.tigo.cam/galaxion-docker/core/frontend/customer-history-admin-ui
      tag: 2.0.1

  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  resources:
    limits:
      cpu: "500m"
      memory: "1025Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

configuration:
  env:
    PORT: 8080
