apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: customer-history-admin-ui
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: common
    helm:
      valueFiles:
      - $values/microservices/core/customer-history-ui/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    targetRevision: 1.3.1
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
