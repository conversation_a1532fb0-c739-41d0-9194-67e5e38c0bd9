configuration:
  env:
    J<PERSON><PERSON>_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SPRING_DATASOURCE_URL: **************************,172.16.79.10:3306/carts
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: keycloak-access/KEYCLOAK_AUTH_SERVER_URL
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: keycloak-access/KEYCLOAK_REALM
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token

  envFrom:
  - configMapRef:
      name: keycloak-access
  - secretRef:
      name: maria-database
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: rabbitmq-creds

dbmdl:
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,172.16.79.10:3306/carts
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  labels:
    owner: itsf
  envFrom:
  - secretRef:
      name: maria-database
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

main:
  useConfigMap: true
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/carts-service
    tag: 5.4.7
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
