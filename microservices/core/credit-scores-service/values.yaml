configuration:
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SPRING_PROFILES_ACTIVE: keycloak-monotenant
    KEYCLOAK_REALMS_REALM_NAME_0: galax<PERSON>
    KEYCLOAK_REALMS_AUTH_SERVER_URL_0: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-credit-scores
    KEYCLOAK_REALMS_SECRET_0: '0123456789'
    SPRING_DATASOURCE_URL: **************************,************:3306/credit_scores

    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password

  envFrom:
    - configMapRef:
        name: rabbitmq
    - secretRef:
        name: rabbitmq-creds
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access
dbmdl:
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/credit_scores
  envFrom:
    - secretRef:
        name: maria-database
  image:
      pullPolicy: IfNotPresent
      repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/credit-scores-service
      tag: 4.1.1
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

main:
  useConfigMap: true
  image:
      pullPolicy: IfNotPresent
      repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/credit-scores-service
      tag: 4.1.1
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"