configuration:
  env:
    J<PERSON><PERSON>_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-contacts
    K<PERSON><PERSON>CL<PERSON>K_REALMS_SECRET_0: '**********'
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_REALMS_SSL_REQUIRED_1: external
    SPRING_DATASOURCE_URL: **************************,************:3306/contacts
    ENVIRONMENT_DEFAULTVALIDATION: 'true'
    ENVIRONMENT_URL_ACCOUNTSSERVICE: 'accounts-service:8080'
    ENVIRONMENT_URL_ADDRESSESSERVICE: 'addresses-service:8080'
    JAVA_TOOL_OPTIONS: '-Xms1024m -Xmx1024m'
    KEYCLOAK_REALMS_AUTH_SERVER_URL_0: keycloak-access/KEYCLOAK_AUTH_SERVER_URL
    KEYCLOAK_REALMS_PRINCIPAL_ATTRIBUTE_0: preferred_username
    KEYCLOAK_REALMS_REALM_NAME_0: galaxion
    KEYCLOAK_REALMS_SSL_REQUIRED_0: external
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    PERMISSION_REMOVAL_DIRECTORY_PATH: /tmp/contact
    PERMISSION_REMOVAL_FEATURE_ENABLED: 'false'
    SERVER_PORT: '8080'
    SPRING_PROFILES_ACTIVE: 'keycloak-monotenant,epic'
    SPRING_RABBITMQ_HOST: rabbitmq
    SPRING_RABBITMQ_PORT: 5672
    SPRING_JPA_SHOW_SQL: true
    SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: true
    LOGGING_LEVEL_ORG_HIBERNATE_SQL: DEBUG
    LOGGING_LEVEL_ORG_HIBERNATE_TYPE_DESCRIPTOR_SQL_BASICBINDER: TRACE
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - configMapRef:
      name: keycloak-access

dbmdl:
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/contacts
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  envFrom:
  - secretRef:
      name: maria-database
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

main:
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/contacts-service
    tag: 7.3.5
  esources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
persistence:
  claims:
  - name: data-contacts
    resourcePolicy: keep
    accessMode: ReadWriteMany
    size: 1Gi
    storageClassName: storage-galaxion
    alreadyExist: true
    mountPath: /tmp/contact
