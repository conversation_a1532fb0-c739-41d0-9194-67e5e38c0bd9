apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: acquisition-prospects-service
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: acquisition-prospects-service
    helm:
      valueFiles:
      - $values/microservices/core/acquisition-prospects-service/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
    targetRevision: 4.4.6
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
