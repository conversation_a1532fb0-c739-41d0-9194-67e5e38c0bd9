configuration:
  env:
    J<PERSON><PERSON>_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    KEYCLOAK_REALMS_REALM_NAME_0: keycloak-access/KEYCLOAK_REALM
    KEYCLOAK_REALMS_AUTH_SERVER_URL_0: keycloak-access/KEYCLOAK_AUTH_SERVER_URL
    KEYCLOAK_REALMS_SSL_REQUIRED_0: external
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-acquisition-prospects
    KEYCLOAK_REALMS_SECRET_0: '0123456789'
    KEYCLOAK_REALMS_PRINCIPAL_ATTRIBUTE_0: preferred_username
    SPRING_DATASOURCE_URL: **************************,************:3306/acquisition_prospects
    DIRECT_DEBIT_ENCRYPTION_PASSWORD: 'ChangeMeIAmAPasswordForEncryption'
    DIRECT_DEBIT_ENCRYPTION_SALT: 'ChangeMeIAmAPasswordForEncryption'
    SECURITY_KEYCLOAK_URL:  keycloak-access/KEYCLOAK_AUTH_SERVER_URL
    SECURITY_KEYCLOAK_REALM: keycloak-access/KEYCLOAK_REALM
    SECURITY_KEYCLOAK_RESOURCE: keycloak-access/KEYCLOAK_RESOURCE
    SECURITY_KEYCLOAK_SECRET: '0123456789'
    SPRING_PROFILES_ACTIVE: keycloak-monotenant
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access

dbmdl:
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  image:
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/acquisition-prospects-service
    tag: 4.4.6
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE --contexts=$LIQUIBASE_CONTEXT update"]
  labels:
    owner: itsf
  envFrom:
  - secretRef:
      name: maria-database
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/acquisition_prospects
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXT: eir

main:
  useConfigMap: true
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/acquisition-prospects-service
    tag: 4.4.6
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
  aliveInitialDelaySeconds: 60
  alivePeriodSeconds: 60
  readyInitialDelaySeconds: 60
  readyPeriodSeconds: 60