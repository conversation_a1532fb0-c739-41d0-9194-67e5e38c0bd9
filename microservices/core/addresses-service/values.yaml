configuration:
  env:
    J<PERSON><PERSON>_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-addresses
    KE<PERSON>CLOAK_REALMS_RESOURCE_1: galaxion-addresses
    KEYCLOAK_REALMS_SECRET_0: '0123456789'
    <PERSON><PERSON><PERSON><PERSON>OAK_REALMS_SECRET_1: '0123456789'
    SPRING_DATASOURCE_URL: **************************,************:3306/addresses
  envFrom:
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access
dbmdl:
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/addresses
    LIQ<PERSON><PERSON>SE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXT: "!eir"
    MIGRATIONS_CARTS_SCHEMA: carts
    MIGRATIONS_EIR_SALES_SCHEMA: sales
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE --contexts=$LIQUIBASE_CONTEXT update"]
  labels:
    owner: itsf
  envFrom:
    - secretRef:
        name: maria-database
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/addresses-service
    tag: 3.2.3
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

main:
  useConfigMap: true
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/addresses-service
    tag: 3.2.3
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
  aliveInitialDelaySeconds: 60
  alivePeriodSeconds: 60
  readyInitialDelaySeconds: 150
  readyPeriodSeconds: 60