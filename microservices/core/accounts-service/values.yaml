global:
  name: accounts-service
  description: Accounts Service
  maintainers:
    - email: <EMAIL>
      name: <PERSON>
  labels:
    team: itsf

configuration:
  envFrom:
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    # Add spring profile 'eir' for EIR
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    ### DATABASE ###
    #SPRING_DATASOURCE_URL: **************************,************:3306/accounts?sessionVariables=wsrep_sync_wait=7
    SPRING_DATASOURCE_URL: **************************,************:3306/accounts
    SPRING_DATASOURCE_USERNAME: maria-database/username
    SPRING_DATASOURCE_PASSWORD: maria-database/password
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_BARRINGSSERVICE: barrings-service:8080
    ENVIRONMENT_URL_BILLINGCYCLESSERVICE: billing-cycles-service:8080
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ENVIRONMENT_URL_CDRREPOSITORYSERVICE: cdr-repository-service:8080
    ENVIRONMENT_URL_CHANGEOFFERSSERVICE: change-offers-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_EARLYCEASECHARGESSSERVICE: early-cease-charges-service:8080
    ENVIRONMENT_URL_EQUIPMENTSSERVICE: equipments-service:8080
    ENVIRONMENT_URL_USAGESSERVICE: usages-service:8080
    ### CACHES ###
    CACHE_ACCOUNT_TTL: '1800000'
    ### B2B SUBSCRIPTION EXTRACT ###
    B2B_SUBSCRIPTION_EXTRACT_ENABLED: true
    B2B_SUBSCRIPTION_EXTRACT_CRON: 0 30 3 * * *
    B2B_SUBSCRIPTION_EXTRACT_DIRECTORY-PATH: TO_BE_DEFINED
    B2B_SUBSCRIPTION_EXTRACT_CRON_LOCK_AT_LEAST: 5m
    B2B_SUBSCRIPTION_EXTRACT_CRON_LOCK_AT_MOST: 5m
    ### PAPER BILL CHARGE ###
    PAPER_BILL_CHARGE_ENABLED: false
    PAPER_BILL_CHARGE_ADDON_CATALOG_CODE: TO_BE_DEFINED
    ### CATALOG CODE ###
    EARLY_CEASE_CHARGE_CATALOG_CODE: ECC_CHARGE
    ### BARRING ###
    BARRING_BLOCKING_CATALOG_CODES:
    BARRING_MINIMUM_BLOCKING_LEVEL: CUSTOMER
    ### EQUIPMENT FINANCING TERMINATION EARLY ###
    CHARGE_CODE_EARLY_REPAYMENT_EQUIPMENTS_FINANCING: EQUIPMENT_FINANCING_EARLY_REPAYMENT_CHARGE


### Only if B2B_SUBSCRIPTION_EXTRACT_ENABLED is enabled ###
#persistence:
#  enabled: true
#  claims:
#    - name: data-accounts
#      resourcePolicy: keep
#      accessMode: ReadWriteMany
#      size: 1Gi
#      storageClassName: silver
#      alreadyExist: false
#      mountPath: /data/
dbmdl:
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "1024Mi"
  image:
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/accounts-service
    tag: 8.4.15
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE --contexts=$LIQUIBASE_CONTEXT update"]
  labels:
    owner: itsf
  envFrom:
  - secretRef:
      name: maria-database
  env:
    #DATASOURCE_URL: **************************,************:3306/accounts?sessionVariables=wsrep_sync_wait=7
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/accounts
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXT: eir

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/accounts-service
    tag: 8.4.15
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"