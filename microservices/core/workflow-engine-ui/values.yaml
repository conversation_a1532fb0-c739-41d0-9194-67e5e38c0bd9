global:
  name: workflow-engine-ui
  description: workflow-engine-ui
  maintainers:
  - email: <EMAIL>
    name: ludo
  labels:
    team: itsf

main:
  useConfigMap: true
  replicas: 1
  securityContext: disabled
  imagePullSecret: nexus-local
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion/monaco-telecom/workflow-engine-ui-tigo
    tag: 1.20.1
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "512Mi"

configuration:
  env:
    REACT_APP_KEYCLOAK_AUTH_PATH: https://glx-iam-pa-stg.tigo.cam/auth
    REACT_APP_KEYCLOAK_AUTH_REALM: galaxion
    REACT_APP_KEYCLOAK_AUTH_CLIENT: workflow-ui
    REACT_APP_KEYCLOAK_MIN_VALIDITY_TOKEN: '30'
    REACT_APP_KEYCLOAK_AUTH_SECRET: "**********"
    REACT_APP_KEYCLOAK_AUTH_CLIENT_PERMISSIONS: galaxion-workflow-query
    REACT_APP_API_URL: https://glx-crm-pa-stg.tigo.cam/workflow-query/private/auth #https://glx-om-query-pa-stg.tigo.cam/private/auth
    REACT_APP_API_DIAGRAM_URL: https://glx-crm-pa-stg.tigo.cam/workflow-query #https://glx-om-query-pa-stg.tigo.cam
    REACT_APP_COMPANY: EPIC
    PORT: '8080'
    REACT_APP_API_WORKFLOW_ENGINE_URL: https://glx-crm-pa-stg.tigo.cam/workflow-engine/private/auth #https://glx-wfe-pa-stg.tigo.cam/private/auth
