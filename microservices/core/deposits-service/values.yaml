configuration:
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-deposits
    K<PERSON><PERSON><PERSON><PERSON>K_REALMS_RESOURCE_1: galaxion-deposits
    K<PERSON><PERSON><PERSON><PERSON>K_REALMS_SECRET_0: '0123456789'
    KEYCLOAK_REALMS_SECRET_1: '0123456789'
    SPRING_DATASOURCE_URL: **************************,172.16.79.10:3306/deposits
  envFrom:
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access

dbmdl:
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,172.16.79.10:3306/deposits
  envFrom:
    - secretRef:
        name: maria-database
  image:
      pullPolicy: IfNotPresent
      repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/deposits-service
      tag: 4.1.0
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"      
main:
  useConfigMap: true
  image:
      pullPolicy: IfNotPresent
      repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/deposits-service
      tag: 4.1.0
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"