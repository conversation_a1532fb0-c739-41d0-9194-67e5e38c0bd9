configuration:
  envFrom:
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    SERVER_PORT: "8080"
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json,payment-run-direct-debit,payment-run-credit-card
    # payment-run-direct-debit: Enable direct debit payment run with file management (PAIN008, PAIN001, PAIN002); payment-run-credit-card: Enable credit card payment run

    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/payments_service
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACCOUNTRECEIVABLEFACADE: account-receivable-facade:8080
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_ADJUSTMENTSSERVICE: adjustments-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_PAYMENTPROVIDERSERVICE: elavon-facade:8080
    ### SPECIFIC PROJECT VALUES ###
    DIRECT_DEBIT_CREDITOR_IDENTIFIER: EIR # Identifier to prefix the generated Unique Mandate Reference (UMR), max 3 chars.
    DIRECT_DEBIT_ENCRYPTION_PASSWORD: ChangeMeIAmAPasswordForEncryption # The password used to generate the encryptor's secret key; should not be shared. IBAN are encrypted using AES-256 algorithm.
    DIRECT_DEBIT_ENCRYPTION_SALT: ChangeMeIAmASaltForEncryption # Salts the password to prevent dictionary attacks against the key.
    DIRECT_DEBIT_COUNTRYCODES_DEFAULT: IE # Default country codes for direct debit country code in ISO 3166-1 format https://en.wikipedia.org/wiki/ISO_3166-1 (can be a list of string i.e: IE,CY,FR).
    DIRECT_DEBIT_COUNTRYCODE_SKIPVALIDATION: false # If set to true will do validation against iban when adding a new direct debit method, reference data must exist in table `ref_bank`.
    DIRECT_DEBIT_FILEMANAGEMENT_DIRECTORYPATH: /data # Root path of to create tree directory to handle PAIN files.
    DIRECT_DEBIT_FILEMANAGEMENT_READINTERVAL: 50 # Interval in ms to check if a file has been uploaded in ingoing directory.
    TRANSACTION_ID_MAX_ATTEMPTS: 100 # Maximum attempts numbers to generate a unique string for a transaction.
    TRANSACTION_ID_LENGTH: 20 # Length of the unique string for a transaction max value is 35 (this transaction id is used in pain file for direct debit, and the max size is 35) .
    NUMBER_HOURS_BETWEEN_NOW_AND_PAYMENT_METHOD_CREATION: 2 # Duration in hours between now and the creation of the payment method, default payment updated notifications are NOT sent if duration is under this value

global:
  name: payments-service
  description: Payments Service
main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/payments-service
    tag: 7.3.2
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
dbmdl:
  image:
    tag: 7.3.2
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/payments_service
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXT: eir
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE --contexts=$LIQUIBASE_CONTEXT update"]
  labels:
    owner: itsf
  envFrom:
  - secretRef:
      name: maria-database
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "1024Mi"

persistence:
  claims:
    - name: data-payments
      resourcePolicy: keep
      accessMode: ReadWriteMany
      size: 1Gi
      storageClassName: galaxion
      alreadyExist: false
      mountPath: /data