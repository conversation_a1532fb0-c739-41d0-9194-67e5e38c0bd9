configuration:
  env:
    J<PERSON><PERSON>_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    ENVIRONMENT_SCHEDULER_CLEARCACHE_CRON: '@daily'
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-catalog
    <PERSON><PERSON><PERSON><PERSON><PERSON>K_REALMS_RESOURCE_1: galaxion-catalog
    <PERSON><PERSON><PERSON>CLOAK_REALMS_SECRET_0: '0123456789'
    KEYCLOAK_REALMS_SECRET_1: '0123456789'
    SPRING_DATASOURCE_URL: **************************,172.16.79.10:3306/catalog
    ROUNDING_METHOD: "CEILING" #UP
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - configMapRef:
      name: keycloak-access

dbmdl:
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,172.16.79.10:3306/catalog
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXT: "!eir"
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE --contexts=$LIQUIBASE_CONTEXT update"]
  envFrom:
  - secretRef:
      name: maria-database
  resources:
    limits:
      cpu: "300m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

main:
  useConfigMap: true
  aliveInitialDelaySeconds: 60
  alivePeriodSeconds: 60
  readyInitialDelaySeconds: 60
  readyPeriodSeconds: 60
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/catalog-service
    tag: 8.3.2
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
