global:
  name: payments-ui
  description: Payments UI

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/frontend/payments-ui
    tag: 1.5.0
  targetPort: 80
  port: 8080
  alivePath: /
  readyPath: /
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"

### If you want override the default theme, fonts and logo ###
#persistence:
#    claims:
#        - name: TO_BE_DEFINED
#          resourcePolicy: keep
#          accessMode: ReadWriteMany
#          size: 500Mi
#          storageClassName: silver
#          alreadyExist: false
#          mountPath: /data/

configuration:
  env:
    API_URL: TO_BE_DEFINED # Payments API
    APP_BRAND: TO_BE_DEFINED # Brand used to filter payment run
    CRM_UI_URL: TO_BE_DEFINED # URL of the CRM ui
    CRM_UI_ACQUISITION_URL: TO_BE_DEFINED # URL of the CRM acquisition ui
    KEYCLOAK_REALM: TO_BE_DEFINED # Realm of keycloak
    KEYCLOAK_CLIENT: TO_BE_DEFINED # Client of keycloak
    KEYCLOAK_URL: TO_BE_DEFINED # URL of keycloak
    KEYCLOAK_JS_URL: TO_BE_DEFINED # URL of the keycloak.js file
    CURRENCY_LOCALE: TO_BE_DEFINED # The locale of the currency to display the country format (example: `en-IE`)
    CURRENCY: TO_BE_DEFINED # The currency (example: `EUR` - full list https://github.com/unicode-org/icu/tree/main/icu4c/source/data/curr)
