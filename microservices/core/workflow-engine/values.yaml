global:
  name: workflow-engine
  description: workflow-engine
  maintainers:
  - email: <EMAIL>
    name: ludo
  labels:
    team: itsf

main:
  useConfigMap: false
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/repository/maven-releases/workflow-engine-pa
    tag: 2.0.1-SNAPSHOT-TSFD-10915
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  resources:
    limits:
      cpu: "2"
      memory: "5Gi"
    requests:
      cpu: "1"
      memory: "5Gi"

configuration:
  env:
    JAVA_TOOL_OPTIONS: -Xms5120m -Xmx5120m
    SERVER_PORT: 8080
    SPRING_PROFILES_ACTIVE: ""
    SPRING_DATASOURCE_URL: **************************,************:3306/workflow_engine?serverTimezone=Europe/Paris
    SPRING_DATASOURCE_HIKARI_MINIMUMIDLE: "3"
    WORKFLOW_ENGINE_DB_DRIVER: com.mariadb.jdbc.Driver
    SPRING_JPA_HIBERNATE_DDLAUTO: update

    TRANSACTION_ISOLATION: 2
    WORKFLOW_INTEGRATION_NB_OF_RETRIES: 3
    WORKFLOW_INTEGRATION_WAIT_FACTOR: 2
    WORKFLOW_INTEGRATION_WAIT_TIME_MS: 5

    # HIKARI
    # SPRING_DATASOURCE_HIKARI_MAXIMUMPOOLSIZE: "10"
    # SPRING_DATASOURCE_HIKARI_CONNECTIONTIMEOUT: "30000"
    # SPRING_DATASOURCE_HIKARI_MAXLIFETIME: "59000"
    # SPRING_DATASOURCE_HIKARI_IDLETIMEOUT: "59000"
    # SPRING_DATASOURCE_HIKARI_CONNECTIONINITSQL: "SET SESSION wait_timeout=60"

    # KEYCLOAK
    KEYCLOAK_RESOURCE: workflow-engine
    KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_CREDENTIALS_SECRET: '0123456789'
  envSecret:
    SPRING_DATASOURCE_USERNAME: maria-database/username
    SPRING_DATASOURCE_PASSWORD: maria-database/password
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - configMapRef:
      name: keycloak-access

# ############################################################################ #
#                                     DBMDL                                    #
# ############################################################################ #
dbmdl:
  envSecret:
    MIGRATIONS_USERNAME: maria-database/username
    MIGRATIONS_PASSWORD: maria-database/password
  env:
    SPRING_DATASOURCE_URL: **************************,************:3306/workflow_engine
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXTS: "salt"
    SPRING_DATASOURCE_DRIVER_CLASS_NAME: org.mariadb.jdbc.Driver
  envFrom:
  - secretRef:
      name: maria-database
  image:
    repository: nexus-tsf.tigo.cam/galaxion/monaco-telecom/workflow-engine-dbmdl
    tag: 1.1.2

  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
