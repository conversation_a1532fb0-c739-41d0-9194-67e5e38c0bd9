configuration:
  env:
    J<PERSON><PERSON>_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    B2B_SUBSCRIPTION_EXTRACT_DIRECTORY-PATH: /data
    ENVIRONMENT_URL_BILLING-ARCHIVE-SERVICE_HISTORYRETRIEVE: TO_BE_DEFINED
    ENVIRONMENT_URL_BILLING-ARCHIVE-SERVICE_PDFRETRIEVE: TO_BE_DEFINED
    ENVIRONMENT_URL_BILLING-ARCHIVE-SERVICE_SALTTOKENRETRIEVE: TO_BE_DEFINED
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-appointments
    KEYCLOAK_REALMS_RESOURCE_1: galaxion-appointments
    KEYCLOAK_REALMS_SECRET_0: '0123456789'
    KEYCLOAK_REALMS_SECRET_1: '0123456789'
    PAPER_BILL_CHARGE_ADDON_CATALOG_CODE: AO_BILL
    SALT_BILLING_REQUESTER_ID: billing-test
    SPRING_DATASOURCE_URL: **************************,************:3306/appointments?sessionVariables=wsrep_sync_wait=7
  envFrom:
    - configMapRef:
        name: rabbitmq
    - secretRef:
        name: rabbitmq-creds
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access
dbmdl:
  image:
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/appointments-service
    tag: 4.1.2
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/appointments
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update"]
  labels:
    owner: tigo
  envFrom:
  - secretRef:
      name: maria-database
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

main:
  useConfigMap: true
  imagePullSecret: nexus-local
  securityContext: disabled
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/appointments-service
    tag: 4.1.2
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"