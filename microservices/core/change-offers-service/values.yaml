configuration:
  env:
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_REALMS_RESOURCE_0: galaxion-change-offers
    KE<PERSON><PERSON><PERSON>K_REALMS_RESOURCE_1: galaxion-change-offers
    KEYCLOAK_REALMS_SECRET_0: "**********"
    K<PERSON><PERSON><PERSON><PERSON><PERSON>_REALMS_SECRET_1: "**********"
    SPRING_DATASOURCE_URL: jdbc:mariadb://***********,************:3306/change_offers
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_ADDONSSERVICE: addons-service:8080
    ENVIRONMENT_URL_APPOINTMENTSSERVICE: appointments-service:8080
    ENVIRONMENT_URL_BARRINGSSERVICE: barrings-service:8080
    ENVIRONMENT_URL_CARTSSERVICE: carts-service:8080
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ENVIRONMENT_URL_COLLECTIONSSERVICE: collections-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_CREDITSCORESSERVICE: credit-scores-service:8080
    ENVIRONMENT_URL_DISCOUNTSSERVICE: discounts-service:8080
    #ENVIRONMENT_URL_DOCUMENTSSERVICE: documents-service:8080
    #ENVIRONMENT_URL_EARLYCEASECHARGESSERVICE: early-cease-charges-service:8080
    ENVIRONMENT_URL_EQUIPMENTSSERVICE: equipments-service:8080
    #ENVIRONMENT_URL_NUMBERSWAPSSERVICE: number-swaps-service:8080
    ENVIRONMENT_URL_UNIQUEREFERENCESSERVICE: unique-references-service:8080
    #ENVIRONMENT_URL_WORKFLOWORDERCREATORSERVICE: workflow-order-creator:8080
    ENVIRONMENT_URL_WORKFLOWQUERYFACADESERVICE: workflow-query-facade:8080
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access

dbmdl:
  env:
    LIQUIBASE_DATASOURCE_URL: jdbc:mariadb://***********,************:3306/change_offers
    LIQUIBASE_CONTEXT: eir
    MIGRATION_ADDRESSES_SCHEMA: addresses
    MIGRATION_CONTACTS_SCHEMA: contacts
  envFrom:
    - secretRef:
        name: maria-database
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/change-offers-service
    tag: 5.4.6

main:
  useConfigMap: true
  aliveInitialDelaySeconds: 60
  alivePeriodSeconds: 60
  readyInitialDelaySeconds: 60
  readyPeriodSeconds: 60
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/change-offers-service
    tag: 5.4.6
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
