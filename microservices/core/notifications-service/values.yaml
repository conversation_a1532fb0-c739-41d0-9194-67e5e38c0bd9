configuration:
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-notifications
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_REALMS_RESOURCE_1: galaxion-notifications
    <PERSON><PERSON><PERSON>CL<PERSON>K_REALMS_SECRET_0: '0123456789'
    KEYCLOAK_REALMS_SECRET_1: '0123456789'
    SPRING_DATASOURCE_URL: **************************,************:3306/notifications
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - secretRef:
        name: maria-database
    - configMapRef:
        name: keycloak-access

dbmdl:
  env:
    LIQUIBASE_DATASOURCE_USERNAME: maria-database/username
    LIQUIBASE_DATASOURCE_PASSWORD: maria-database/password
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/notifications
  envFrom:
    - secretRef:
        name: maria-database
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/notifications-service
    tag: 5.2.8
  resources:
    limits:
      cpu: "300m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

main:
  useConfigMap: true
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/notifications-service
    tag: 5.2.8
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"