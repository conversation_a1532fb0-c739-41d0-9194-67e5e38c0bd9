configuration:
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/eir_sales_facade
    ### RABBITMQ ###
    SPRING_RABBITMQ_HOST: "************"
    SPRING_RABBITMQ_PORT: "5672"
    SPRING_RABBITMQ_USERNAME: "glxpa"
    SPRING_RABBITMQ_PASSWORD: "Soporte2025!"
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_ACQUISITIONPROSPECTSSERVICE: acquisition-prospects-service:8080
    ENVIRONMENT_URL_ADDRESSESSERVICE: addresses-service:8080
    ENVIRONMENT_URL_ADDONSSERVICE: add-ons-service:8080
    ENVIRONMENT_URL_CARTSSERVICE: carts-service:8080
    ENVIRONMENT_URL_CATALOGSERVICE: catalog-service:8080
    ENVIRONMENT_URL_CHANGEOFFERSSERVICE: change-offers-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_CONTRACTBUILDER: contract-builder-service:8080
    ENVIRONMENT_URL_CREDITSCORESSERVICE: credit-scores-service:8080
    ENVIRONMENT_URL_CROSSSELLSERVICE: cross-sell-service:8080
    ENVIRONMENT_URL_EIRBARRINGSSERVICE: eir-barrings-service:8080
    ENVIRONMENT_URL_EIRCVSSERVICE: TO_BE_DEFINED
    ENVIRONMENT_URL_EIRPREQUALSERVICE: TO_BE_DEFINED
    ENVIRONMENT_URL_EQUIPMENTSSERVICE: equipments-service:8080
    ENVIRONMENT_URL_MNPFACADE: mnp-facade:8080
    ENVIRONMENT_URL_OTPVERIFICATIONSERVICE: otp-verification-service:8080
    ENVIRONMENT_URL_PAYMENTSSERVICE: payments-service:8080
    ENVIRONMENT_URL_SECURITYQUESTIONSSERVICE: security-questions-service:8080
    ENVIRONMENT_URL_UGINTEGRATIONSERVICE: ug-integration-service:8080
    ### CACHE DURATION ###
    TIMETOLIVE_CACHE_CREDITSCORESSERVICE: '18000000'
    TIMETOLIVE_CACHE_CONTACTSSERVICE: '18000000'
    TIMETOLIVE_CACHE_UGINTEGRATIONSERVICE: '18000000'
    TIMETOLIVE_CACHE_CATALOGSERVICE: '18000000'
    ### APPOINTMENT FARTHEST SLOT DAYS ###
    APPOINTMENT_FARTHEST_SLOT_DAYS: '28'
    ### ADDONS CODE LIFE CYCLE INCOMPATIBLE ###
    ADDONS_CODE_LIFE_CYCLE_INCOMPATIBLE: PANELLIST
    ### Environment source for UG (called by Prequal Service) : (EIR_DEV, PERF, ...) ###
    PREQUAL_ENVIRONMENT_SOURCE: 'TO_BE_DEFINED'
    DEFAULT_OFFER_SERVICE_GROUP: 'DUALPLAY'
    DEFAULT_OPTIONAL_INSTALLATION_ADDON_CODE: 'OPTIONAL_APPOINTMENT_BB'
    ### SCHEDULER
    ENVIRONMENT_SCHEDULER_PREQUAL_CLEANER: '-'
    ### INSURANCE ###
    INSURANCE_ADDON_ITEM_GROUP_CODE: INSURANCE # Use to filter catalog addons by item group code in addon response for equipment
  envFrom:
    - secretRef:
        name: maria-database

global:
  name: eir-sales-facade
  description: Eir Sales Facade
main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/eirie/backend/eir-sales-facade
    tag: 4.2.23
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "500m"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "2Gi"
dbmdl:
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "1024Mi"
  image:
    tag: 4.2.23
  command: ["sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$LIQUIBASE_DATASOURCE_USERNAME --password=$LIQUIBASE_DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE  --contexts=$LIQUIBASE_CONTEXTS update"]
  labels:
    owner: itsf
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/eir_sales_facade
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXTS: eir
  envFrom:
    - secretRef:
        name: maria-database
#virtualservice:
#  gateways:
#  - galaxion-eir-int-gateway
#  hosts:
#  - "eir-sales-facade.eir-int.itsf.io"