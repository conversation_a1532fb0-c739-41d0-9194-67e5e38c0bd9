apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: eir-sales-facade
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - chart: eir-sales-facade
      helm:
        valueFiles:
          - $values/microservices/core/eir-sales-facade/values.yaml
      ref: helm
      repoURL: https://nexus-tsf.tigo.cam/repository/galaxion-helm-delivered/
      targetRevision: 4.2.23
    - ref: values
      repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
      targetRevision: stg