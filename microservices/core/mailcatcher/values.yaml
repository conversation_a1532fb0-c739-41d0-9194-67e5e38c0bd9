global:
  name: mailcatcher
  description: Malta Mail Catcher
  maintainers:
    - email: <EMAIL>
      name: Maxime PRUNIER
  labels:
    team: itsf

main:
  replicas: 1
  securityContext: enabled
  noHealthProbes: true
  image:
    pullPolicy: IfNotPresent
    repository: schickling/mailcatcher
    tag: latest
  servicePorts:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 1080
    - name: smtp
      port: 1025
      protocol: TCP
      targetPort: 1025
  livenessProbe:
    httpGet:
      path: /
      port: http
  readinessProbe:
    httpGet:
      path: /
      port: http
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

configuration: {}





