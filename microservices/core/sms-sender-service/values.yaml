configuration:
  envSecret:
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password
 
  env:
    ### SERVER ###
    JAVA_OPTS: "-XX:MaxRAMPercentage=75.0 -XX:+UseContainerSupport -XX:+UseG1GC"
    ### RABBITMQ ###
    SPRING_RABBITMQ_ADDRESSES: rabbitmq/SPRING_RABBITMQ_ADDRESSES
    SPRING_RABBITMQ_USERNAME: rabbitmq-creds/rabbitmq-username
    SPRING_RABBITMQ_PASSWORD: rabbitmq-creds/rabbitmq-password
  ### SERVER ###
  #JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
  #SERVER_PORT: 8080
  ### LOGS ###
  #LOGGING_LEVEL_FR_NJJ_GALAXION: TRACE
  ### SPRING ###
  #SPRING_PROFILES_ACTIVE: output-logs-as-json,openmind  # openmind or smpp
  ### RABBITMQ ###
  #SPRING_RABBITMQ_HOST: TO_BE_DEFINED
  #SPRING_RABBITMQ_PORT: TO_BE_DEFINED
  #AMQP_EXCHANGE_TEXT_MESSAGE: notification-center
  #AMQP_ROUTING_KEY_SMS_CENTER_ID: sms.smscid
  #AMQP_ROUTING_KEY_DELIVERY_RECEIPT: sms.delivery-receipt
  #AMQP_QUEUE_TEXT_MESSAGE_SEND: sms.send

  ### Openmind - To fill only if "openmind" profile is active ###
  #OPENMIND_URL: TO_BE_DEFINED

  ### SMPP - To fill only if "smpp" profile is active ###
  # SMPP_IP: TO_BE_DEFINED
  # SMPP_PORT: TO_BE_DEFINED
  # SMPP_SYSTEMID: TO_BE_DEFINED
  # SMPP_SYSTEM_TYPE: TO_BE_DEFINED
  # SMPP_INTERFACE_VERSION: 34
  # SMPP_RECONNECT_INITIAL_DELAY: 5000
  # SMPP_RECONNECT_INTERVAL: 1000

  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq

main:
  useConfigMap: true
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/backend/sms-sender-service
    tag: 3.1.2
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"

