configuration:
  env:
    SPRING_PROFILES_ACTIVE:
    ENVIRONMENT_WEBSERVICE_KEYCLOAK_TOKENURL: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    KEY<PERSON>OAK_CREDENTIALS_SECRET: '0123456789'
    KEYCLOAK_RESOURCE: galaxion
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - configMapRef:
        name: keycloak-access

main:
  useConfigMap: true
  image:
    repository: nexus-tsf.tigo.cam/galaxion/tecrep-equipments-workflow-connector
    tag: 1.7.10
