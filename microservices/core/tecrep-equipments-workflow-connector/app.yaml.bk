apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tecrep-equipments-workflow-connector
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: tecrep-equipments-workflow-connector
    helm:
      valueFiles:
      - $values/microservices/core/tecrep-equipments-workflow-connector/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: 1.7.10
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
