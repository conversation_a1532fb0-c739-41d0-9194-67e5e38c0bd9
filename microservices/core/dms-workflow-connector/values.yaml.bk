configuration:
  env:
    KE<PERSON><PERSON><PERSON>K_CREDENTIALS_SECRET: '0123456789'
    KEYCLOAK_RESOURCE: galaxion
  envFrom:
    - secretRef:
        name: rabbitmq-creds
    - configMapRef:
        name: rabbitmq
    - configMapRef:
        name: keycloak-access

main:
  useConfigMap: true
  image:
      pullPolicy: IfNotPresent
      repository: nexus-tsf.tigo.cam/galaxion/dms-workflow-connector
      tag: 2.1.0
  resources:
    limits:
      cpu: "500m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"