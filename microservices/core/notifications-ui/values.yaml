global:
  name: notifications-ui
  description: Notifications UI
main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion-docker/core/frontend/notifications-ui
    tag: 2.2.0
  targetPort: 8080
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "1"
      memory: "2Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"
configuration:
  env:
    BACKEND_API_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/notifications-service/
    KEYCLOAK_CLIENT: galaxion-notifications-ui
    KEYCLOAK_JS_URL: https://glx-iam-pa-stg.tigo.cam/auth/js/keycloak.js
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_URL: https://glx-iam-pa-stg.tigo.cam/auth
    PRIVATE_ENDPOINT_PREFIX: 'private/'
    AUTHENTICATED_ENDPOINT_PREFIX: 'auth/'