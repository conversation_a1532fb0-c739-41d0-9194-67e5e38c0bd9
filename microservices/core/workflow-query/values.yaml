global:
  name: workflow-query
  description: Workflow Query
  maintainers:
  - email: d.boy<PERSON><PERSON>@monaco-telecom.mc
    name: <PERSON>
  labels:
    team: monacotelecom

main:
  useConfigMap: true
  replicas: 1
  noHealthProbes: true
  imagePullSecret: nexus-local
  timezone: "America/Bogota"
  securityContext: disabled
  image:
    pullPolicy: IfNotPresent
    repository: nexus-tsf.tigo.cam/galaxion/monaco-telecom/workflow-query
    tag: 2.2.0
  port: 8080
  targetPort: 8080
  alivePath: /actuator/health
  aliveInitialDelaySeconds: 280
  alivePeriodSeconds: 60
  readyPath: /actuator/health
  readyInitialDelaySeconds: 280
  readyPeriodSeconds: 60
  resources:
    limits:
      cpu: "2"
      memory: "5Gi"
    requests:
      cpu: "1"
      memory: "2Gi"

configuration:
  envFrom:
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - configMapRef:
      name: keycloak-access

  env:
    # ---------------------------------- Server ---------------------------------- #
    SERVER_PORT: 8080
    JAVA_TOOL_OPTIONS: -Xms2048m -Xmx2048m -Dserver.max-http-header-size=256KB -Duser.timezone=America/Bogota
    SERVER_MAX_HTTP_HEADER_SIZE: 65536
    # ---------------------------------- Spring ---------------------------------- #
    SPRING_DATASOURCE_URL: jdbc:mariadb://***********,************:3306/workflow_query?serverTimezone=America/Bogota
    SPRING_DATASOURCE_HIKARI_MINIMUMIDLE: "3"
    SPRING_JPA_HIBERNATE_DDLAUTO: update
    SPRING_PROFILES_ACTIVE: output-logs-as-json
    # --------------------------------- Rabbit MQ -------------------------------- #

    WORKFLOW_ENGINE_DB_DRIVER: com.mariadb.jdbc.Driver
    SPRING_JPA_DATABASEPLATFORM: org.hibernate.dialect.MariaDB103Dialect

    # ---------------------------------- HIKARI ---------------------------------- #
    SPRING_DATASOURCE_HIKARI_MAXIMUMPOOLSIZE: "10"
    SPRING_DATASOURCE_HIKARI_CONNECTIONTIMEOUT: "30000"
    SPRING_DATASOURCE_HIKARI_MAXLIFETIME: "59000"
    SPRING_DATASOURCE_HIKARI_IDLETIMEOUT: "59000"
    SPRING_DATASOURCE_HIKARI_CONNECTIONINITSQL: "SET SESSION wait_timeout=60"
    # --------------------------------- KEYCLOAK --------------------------------- #
    KEYCLOAK_RESOURCE: galaxion-workflow-query
    KEYCLOAK_CREDENTIALS_SECRET: '0123456789'

    # -------------------------------- WebService -------------------------------- #
    WORKFLOW_ENGINE_CLIENT_BASEURL: http://workflow-engine:8080

# ############################################################################ #
#                                     DBMDL                                    #
# ############################################################################ #
dbmdl:
  labels:
    netpol-galaxion-dev-epic-malta: 'true'
    netpol-vip-galaxionepicmt-dev-mysql: 'true'
  image:
    #repository: nexus-tsf.tigo.cam/docker-itsf/v2/monaco-telecom/workflow-query-dbmdl
    #repository: nexus-tsf.tigo.cam/galaxion-docker/core/workflow-engine/workflow-query/workflow-query-liquibase
    repository: nexus-tsf.tigo.cam/galaxion/monaco-telecom/workflow-query-dbmdl
    tag: 2.2.5

  envSecret:
    MIGRATIONS_USERNAME: "maria-database/DATASOURCE_USERNAME"
    MIGRATIONS_PASSWORD: "maria-database/DATASOURCE_PASSWORD"
   
  env:
    SPRING_DATASOURCE_URL: jdbc:mariadb://***********,************:3306/workflow_query
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    LIQUIBASE_CONTEXTS: epicmt
    SPRING_DATASOURCE_DRIVER_CLASS_NAME: org.mariadb.jdbc.Driver
    DB_IGNORE_SEQUENCE_CREATION: true
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
