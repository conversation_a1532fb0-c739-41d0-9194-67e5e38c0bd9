apiVersion: v1
kind: ConfigMap
metadata:
  name: keycloak-access
data:
  KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
  KEYCLOAK_AUTHSERVERURL: https://glx-iam-pa-stg.tigo.cam/auth
  KEYCLOAK_REALMS_AUTH_SERVER_URL_0: https://glx-iam-pa-stg.tigo.cam/auth
  KEYCLOAK_REALMS_AUTH_SERVER_URL_1: https://glx-iam-pa-stg.tigo.cam/auth
  KEYCLOAK_REALM: galaxion
  KEYCLOAK_RESOURCE: galaxion
  KEYCLOAK_REALMS_REALM_NAME_0: galaxion
  KEYCLOAK_REALMS_REALM_NAME_1: galaxion