main:
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/glx/glx-pa/orchestration-tools/workflow-engine/workflow-engine-facade
    tag: 2.0.3-SNAPSHOT-10999
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: TSF
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"


configuration:
  envFrom:
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: rabbitmq-creds
  env:
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: output-logs-as-json,keycloak-monotenant
    FEIGN_CLIENT_CONFIG_DEFAULT_LOGGERLEVEL: FULL
    MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: '*'
    MANAGEMENT_ENDPOINT_LOGGERS_ENABLED: true
    #HTTP_PROXY: "proxyserver.epmtelco.com.co:8080"
    #HTTPS_PROXY: "proxyserver.epmtelco.com.co:8080"
    #NO_PROXY: "titvm-*,*********/16,127.0.0.1,10.0.0.0/16,*********/16,localhost,*********/16,.cluster.local,2001:cafe:42:0::/56,2001:cafe:42:1::/112,gitlab-tsf.tigo.cam,localadress,.localdomain.com,*********/8"
    ### KEYCLOAK ###
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_AUTHORIZATIONGRANTTYPE: client_credentials
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: galaxion-malta-workflow-engine-facade
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: https://glx-iam-pa-stg.tigo.cam/auth
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token

    ### TERMINATION ADDONS ###
    TERMINATIONADDON_MOBILE_MIGRATION.TOPREPAID: MIGRATION_TO_PREPAID
    TERMINATIONADDON_MOBILE_FMS.EQUIPMENTNOTRETURNED: FMS_CPE_NOT_RETURNED
