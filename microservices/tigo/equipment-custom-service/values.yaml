global:
  name: equipment-custom-service
  description: Equipment Service
  maintainers:
  - email: <EMAIL>
    name: <PERSON>
  labels:
    team: itsf

main:
  replicas: 1
  securityContext: enabled
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/backend/equipment-custom-service
    tag: 1.0.2-SNAPSHOT

  port: 8082
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: TSF
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "50m"
      memory: "256Mi"
dbmdl:
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
  image:
    tag: 1.0.1-SNAPSHOT
  command:
    [
      "sh",
      "-c",
      "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$DATASOURCE_USERNAME --password=$DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update",
    ]
  labels:
    owner: TSF
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/test
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  envFrom:
    - secretRef:
        name: maria-database

configuration:
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8082
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/equipments_management
  envFrom:
    - secretRef:
        name: maria-database