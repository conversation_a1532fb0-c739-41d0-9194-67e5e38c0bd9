apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: equipments-pa-service
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: equipment-custom-service
    helm:
      valueFiles:
      - $values/microservices/tigo/equipment-custom-service/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: "1.0.x-SNAPSHOT"
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg