#.
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tigo-crm-api-gateway
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: tigo-b2c-crm-api-gateway
    helm:
      valueFiles:
      - $values/microservices/tigo/tigo-crm-api-gateway/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: 1.13.x-SNAPSHOT
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
