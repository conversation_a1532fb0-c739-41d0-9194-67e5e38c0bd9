main:
  replicas: 1
  securityContext: disabled
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/tigo/tigo-crm-api-gateway
    tag: 1.16.4-SNAPSHOT-10400
  port: 8080
  alivePath: /__health
  readyPath: /__health
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "100m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
global:
  name: tigo-crm-api-gateway
  description: TIGO B2C API Gateway
  maintainers:
  - email: <EMAIL>
    name: Maxime PRUNIER
  labels:
    team: itsf
    netpol-galaxion-dev-epic-malta: 'true'

configuration:
  env:
    ### Auth Validator ###
    KRAKEND_AUTH_VALIDATOR_JWK_URL: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/certs
    KRAKEND_AUTH_VALIDATOR_TLS_CA_PATH: /etc/krakend/certificates/root.pem
    KRAKEND_AUTH_VALIDATOR_DEBUG: true
    KRAKEND_AUTH_VALIDATOR_ROLES_KEY: resource_access.b2c-crm-api-gateway.roles

    ### Specific External Services with oauth
    KRAKEND_TIGO_ADDRESS_API_HOST: https://address-management-service.api.dev.mla.epicmt.internal
    KRAKEND_TIGO_ADDRESS_OAUTH_URL: https://auth.dev.mla.epicmt.internal/legacy/oauth/token
    KRAKEND_TIGO_ADDRESS_OAUTH_ID: GLX_API_GW
    KRAKEND_TIGO_ADDRESS_OAUTH_SECRET: SyF32redhGiKGicKFckRLDe7ZUM8SS

    KRAKEND_MALTA_NIF_BSP_API_HOST: http://malta-custom-mock-service:8080
    KRAKEND_MALTA_NIF_BSP_OAUTH_URL: https://auth.dev.mla.epicmt.internal/legacy/oauth/token
    KRAKEND_MALTA_NIF_BSP_OAUTH_ID: GLX_WFE_CONNECTOR
    KRAKEND_MALTA_NIF_BSP_OAUTH_SECRET: EmFTiJ0x6fbsBk8AXWOC97LjkEak70

    KRAKEND_POS_MALTA_API_HOST: http://malta-custom-mock-service:8080

    ### Logging ###
    KRAKEND_LOG_LEVEL: DEBUG
    ACTIVATE_BACKEND_LOGGER_REQUEST: true
    ACTIVATE_BACKEND_LOGGER_RESPONSE: true

    ### External Services ###
    KRAKEND_ACCOUNTS_API_HOST: http://accounts-service:8080
    KRAKEND_ACQUISITION_PROSPECTS_API_HOST: http://acquisition-prospects-service.glx-pa.svc:8080
    KRAKEND_ADDONS_API_HOST: http://add-ons-service:8080
    KRAKEND_CATALOG_API_HOST: http://catalog-service:8080
    KRAKEND_TIGO_SALES_FACADE_API_HOST: http://tigo-sales-facade.glx-pa.svc:8080
    KRAKEND_TIGO_GEOGRAPHIC_MANAGEMENT_API_HOST: http://geographic-management.glx-pa.svc:8082
    KRAKEND_TIGO_SERVICE_COVERAGE_API_HOST: http://service-coverage-mock-up.glx-pa.svc:8082
    KRAKEND_TIGO_WFE_FACADE_API_HOST: http://workflow-engine-facade:8080
    KRAKEND_TIGO_WFE_QUERY_FACADE_API_HOST: http://workflow-query-facade:8080
    KRAKEND_SEARCH_ENGINE_API_HOST: http://search-engine-service:8080
    KRAKEND_ACCOUNT_RECEIVABLE_FACADE_API_HOST: http://account-receivable-facade:8080
    KRAKEND_ACCOUNT_SERVICE_API_HOST: http://accounts-service:8080
    KRAKEND_NOTIFICATIONS_API_HOST: http://notifications-service:8080
    KRAKEND_COLLECTION_API_HOST: http://collections-service:8080
    KRAKEND_CONTACT_API_HOST: http://contacts-service:8080
    KRAKEND_UNIQUE_REFERENCES_SERVICE: http://unique-references-service:8080
    KRAKEND_BILLING_CYCLE_API_HOST: http://billing-cycles-service:8080
    KRAKEND_EQUIPMENTS_API_HOST: http://equipments-service:8080

    KRAKEND_CUSTOMER_MANAGEMENT_API_HOST: http://customer-management:8082
    KRAKEND_FS_MILLICOM_API_HOST: https://fs-ar2-test.fsmillicom.com:8080
    KRAKEND_OFFERS_API_HOST: http://************:8095
    KRAKEND_CASE_MANAGEMENT_SERVICE_API_HOST: http://case-management-pa-service:8080
    KRAKEND_CASE_MANAGEMENT_USERS_WORKER_API_HOST: http://case-management-pa-users-worker:8080
    KRAKEND_CASE_MANAGEMENT_AUDIT_WORKER_API_HOST: http://case-management-pa-audit-worker:8080
    KRAKEND_CASE_MANAGEMENT_TASK_SERVICE_API_HOST: http://case-management-pa-tasks:8080
    KRAKEND_USERS_MANAGEMENT_API_HOST: http://users-management-pa:8080
    KRAKEND_CASE_MANAGEMENT_FACADE_API_HOST: http://case-management-facade-pa:8080
    KRAKEND_ACCOUNTS_RECEIVABLE_API_HOST: http://account-receivable-service:8080
    KRAKEND_CASTLEMOCK_API_HOST: http://castlemock.mocks:8080
    KRAKEND_CASTLEMOCK_API_PREFIX: "/castlemock/mock/rest/project/vbAzyb/application/Dz1B7F"
    KRAKEND_ORDER_STATUS_API_HOST: http://order-status-facade.glx-pa.svc:8080
    KRAKEND_AR_PAYMENT_FACADE_API_HOST: http://ar-payment-facade:8080
    KRAKEND_BILLING_API_HOST: http://billing-api:8080