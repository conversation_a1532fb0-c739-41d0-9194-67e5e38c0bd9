main:
  securityContext: disabled
  replicas: 1
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/glx/glx-pa/front-end/b2c-crm-ui
    tag: 1.1.2-10969
  port: 8080
  alivePath: /
  readyPath: /
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "300m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
#
configuration:
  env:
    #TEST: "test"
    APP_BRAND: TIGO
    #APP_ENV: staging
    BACKEND_API_URL: https://glx-crm-pa-stg.tigo.cam/
    API_URL: https://glx-crm-pa-stg.tigo.cam/
    WFE_UI_URL: https://glx-wfe-pa-stg.tigo.com.co
    CONFIG: "%7B%22featureFlags%22:%7B%22identityChecker%22:false%7D%7D"
    # Keycloak config
    KEYCLOAK_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_CLIENT: galaxion-crm-ui
    KEYCLOAK_JS_URL: https://glx-iam-pa-stg.tigo.cam/auth/js/keycloak.js
    # Keycloak clients
    API_GATEWAY_ENDPOINT_PREFIX: "" # "api-gateway/"
    PROSPECT_CLIENT_NAME: galaxion-prospects # Prospect client name on keycloak
    ACQUISITION_PROSPECTS_CLIENT_NAME: galaxion-acquisition-prospects # Acquisition prospects client name on keycloak
    ACCOUNT_CLIENT_NAME: galaxion-accounts # Account client name on keycloak
    CHANGE_OFFER_CLIENT_NAME: galaxion-change-offers # Change offer client name on keycloak
    NUMBER_SWAPS_CLIENT_NAME: galaxion-number-swaps # Number Swaps client name on keycloak
    ADDONS_CLIENT_NAME: galaxion-add-ons # Equipments client name on keycloak
    EQUIPMENTS_CLIENT_NAME: galaxion-equipments # Add-ons client name on keycloak
    DISCOUNTS_CLIENT_NAME: galaxion-discounts # Discounts client name on keycloak
    WORKFLOW_ORDER_CREATOR_CLIENT_NAME: workflow-order-creator # Workflow order creator client name on keycloak
    EIR_TERMINATION_RULES_VALIDATOR_CLIENT_NAME: galaxion-eir-termination-rules-validator # Eir termination rules validator client name on keycloak
    EIR_EQUIPMENT_REPLACEMENT_VALIDATOR_CLIENT_NAME: galaxion-eir-equipment-replacement-validator # Eir equipment replacement validator client name on keycloak
    API_GATEWAY_CLIENT_NAME: b2c-crm-api-gateway # Api gateway client name on keycloak
    WORKFLOW_UI_CLIENT_NAME: workflow-ui # Workflow UI client name on keycloak
    UPLOAD_MAX_FILE_SIZE_IN_MB: 25 # Size limit per file to upload
    APP_DEFAULT_LANGUAGE: "es-PA"
    CASE_MANAGEMENT_REMOTE: https://glx-crm-pa-stg.tigo.cam/case-management-ui/assets/remoteEntry.js
    CART_VAT_INCLUDED: true
    TASK_TYPE_NEW_SUBSCRIPTION: "Ventas Nuevas FTTH FTTH-B2C"
    TASK_TYPE_NEW_RESCHEDULE: "Daños FTTH FTTH-B2C"
    TASK_TYPE_NEW_SCHEDULE: "Daños FTTH FTTH-B2C"
    TASK_TYPE_ADDONS: "Salidas adicionales FTTH-B2C"
    TASK_CATEGORY_ADDONS: "Salidas adicionales FTTH-B2C"
    ENVIRONMENT_TIME_INTERVAL_NUMBER: "0"
    ENVIRONMENT_TIME_INTERVAL_AREA: "PA-B2C"
    ENVIRONMENT_TIME_INTERVAL_DISTRICT: "PA-SAN FRANCISCO PTY"
    ENVIRONMENT_TIME_INTERVAL_PRIORITY: "1"
    ENVIRONMENT_TIME_INTERVAL_COUNTRYID: "Panama"
    ENVIRONMENT_TIME_INTERVAL_CITY: "PANAMA"
    ENVIROMENT_REASON_CANCEL: "CANCELADO, FALLIDO SIN VISITA"