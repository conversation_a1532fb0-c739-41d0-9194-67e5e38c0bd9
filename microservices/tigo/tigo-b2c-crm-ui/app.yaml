#..
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tigo-b2c-crm-ui
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: tigo-b2c-crm-ui
    helm:
      valueFiles:
      - $values/microservices/tigo/tigo-b2c-crm-ui/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: "x.x.x-x"
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
