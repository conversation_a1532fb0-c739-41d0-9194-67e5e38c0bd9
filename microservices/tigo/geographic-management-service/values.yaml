main:
  replicas: 1
  securityContext: enabled
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/glx/glx-pa/backend/geographic-management-service
    tag: 1.0.1-SNAPSHOT

  port: 8082
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: TSF
  resources:
    limits:
      cpu: "250m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

dbmdl:
  resources:
    limits:
      cpu: "1"
      memory: "1024Mi"
    requests:
      cpu: "500m"
      memory: "512Mi"
  image:
    tag: 1.0.1-SNAPSHOT
  command: [ "sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$DATASOURCE_USERNAME --password=$DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update" ]
  labels:
    owner: TSF
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/geographic_management
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
  envFrom:
    - secretRef:
        name: maria-database

configuration:
  envFrom:
    - configMapRef:
        name: keycloak-access
    - secretRef:
        name: maria-database

  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8082
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/geographic_management
    ### RABBITMQ ###
    # SPRING_RABBITMQ_USERNAME: TO_BE_DEFINED
    # SPRING_RABBITMQ_PASSWORD: TO_BE_DEFINED
    # SPRING_RABBITMQ_ADDRESSES: TO_BE_DEFINED
    # SPRING_RABBITMQ_VIRTUALHOST: TO_BE_DEFINED
    ### WEBSERVICES URLS ###
    # ENVIRONMENT_URL_ACQUISITIONPROSPECTSSERVICE: acquisition-prospects-service:8080
    # ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    # ENVIRONMENT_URL_ADDRESSESSERVICE: addresses-service:8080
    # ENVIRONMENT_URL_CROSSSELLSERVICE: cross-sell-service:8080
    # ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    # ENVIRONMENT_URL_CREDITSCORESSERVICE: credit-scores-service:8080
    # ENVIRONMENT_URL_COLLECTIONSSERVICE: collections-service:8080
    # ENVIRONMENT_URL_TIGOCREDITSCORESSERVICE: TO_BE_DEFINED
    ### CACHE DURATION ###
    # TIMETOLIVE_CACHE_CONTACTSSERVICE: '********'
    ### GET TOKEN TIGO OAUTH ###
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_TIGO_CLIENTID: galaxion
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_TIGO_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_TIGO_AUTHORIZATIONURI: TO_BE_DEFINED
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_TIGO_TOKENURI: TO_BE_DEFINED

    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: galaxion
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: TO_BE_DEFINED
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: TO_BE_DEFINED

    # ==================================
    # API External URLs (Geolocation)
    # ==================================
    EXTERNAL_API_PROVINCES: https://devapi-management.tigo.cam/homepassed/provincias
    EXTERNAL_API_DISTRICTS: https://devapi-management.tigo.cam/homepassed/distritos/{id_provincia}
    EXTERNAL_API_TOWNSHIP: https://devapi-management.tigo.cam/homepassed/corregimientos/{id_distrito}
    EXTERNAL_API_NEIGHBORHOOD: https://devapi-management.tigo.cam/homepassed/barrios/{id_corregimiento}
    EXTERNAL_API_STREET: https://devapi-management.tigo.cam/homepassed/calles/{id_barrio}
    EXTERNAL_API_HOUSE: https://devapi-management.tigo.cam/homepassed/casas/{id_calle}
