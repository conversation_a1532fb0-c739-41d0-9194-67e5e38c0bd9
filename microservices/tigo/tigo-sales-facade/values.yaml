main:
  useConfigMap: true
  replicas: 1
  securityContext: enabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-pa/integrations-tools/tigo-sales-facade
    tag: 1.0.2-SNAPSHOT-108401
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  aliveInitialDelaySeconds: 90
  alivePeriodSeconds: 90
  readyInitialDelaySeconds: 90
  readyPeriodSeconds: 90
  labels:
    owner: tsf
  resources:
    limits:
      cpu: "300m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

dbmdl:
  resources:
    limits:
      cpu: "300m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

  image:
    tag: 1.0.1-SNAPSHOT-63383
  command: [ "sh", "-c", "docker-entrypoint.sh --url=$LIQUIBASE_DATASOURCE_URL --username=$DATASOURCE_USERNAME --password=$DATASOURCE_PASSWORD --classpath=/liquibase/changelog --changeLogFile=$LIQUIBASE_CHANGELOGFILE update" ]
  labels:
    owner: itsf
  env:
    LIQUIBASE_DATASOURCE_URL: **************************,************:3306/sales
    LIQUIBASE_CHANGELOGFILE: db/changelog/db.changelog-master.yaml
    GALAXION_USER_IDENTIFIER: SYSTEM
    GALAXION_USER_TYPE: SYSTEM
  envFrom:
  - secretRef:
      name: maria-database

configuration:
  envFrom:
  - configMapRef:
      name: rabbitmq
  - secretRef:
      name: maria-database
  - secretRef:
      name: rabbitmq-creds
  - configMapRef:
      name: keycloak-access
  env:
    #HTTP_PROXY: "http://***********:8080"
    #HTTPS_PROXY: "http://***********:8080"
    #NO_PROXY: "glx-iam.tigo.com.co,************,titvm-*,*********/16,127.0.0.1,10.0.0.0/16,*********/16,localhost,*********/16,.cluster.local,2001:cafe:42:0::/56,2001:cafe:42:1::/112,gitlab-tsf.tigo.cam,localadress,.localdomain.com,*********/8"
    FEIGN_CLIENT_CONFIG_DEFAULT_LOGGERLEVEL: FULL
    ### HEADERS ###
    GALAXION_USER_IDENTIFIER: SYSTEM
    GALAXION_USER_TYPE: SYSTEM
    ### SERVER ###
    JAVA_TOOL_OPTIONS: "-Xms1024m -Xmx1024m -Djavax.net.debug=ssl:handshake:verbose"
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,************:3306/sales
    ### RABBITMQ ###
    # From ConfigMap
    ### WEBSERVICES URLS ###
    ENVIRONMENT_URL_ACQUISITIONPROSPECTSSERVICE: acquisition-prospects-service:8080
    ENVIRONMENT_URL_CONTACTSSERVICE: contacts-service:8080
    ENVIRONMENT_URL_ADDRESSESSERVICE: addresses-service:8080
    ENVIRONMENT_URL_CROSSSELLSERVICE: cross-sell-service:8080
    ENVIRONMENT_URL_ACCOUNTSSERVICE: accounts-service:8080
    ENVIRONMENT_URL_CREDITSCORESSERVICE: credit-scores-service:8080
    ENVIRONMENT_URL_COLLECTIONSSERVICE: collections-service:8080
    ENVIRONMENT_URL_TIGOPROSPECTLEADSERVICE: prospect-lead:8082
    ENVIRONMENT_URL_EXTERNALCUSTOMERSEARCHSERVICE: https://gatewayhome.tigo.com.co:30443/customervalidation
    ENVIRONMENT_URL_TIGOCREDITSCORESSERVICE: TO_BE_DEFINED
    ENVIRONMENT_URL_TIGOGEOREFERENCESERVICE: http://microcks.mocks.svc.cluster.local:8080/rest/Georeferencing+and+Normalize+Address/1.0/v1
    ENVIRONMENT_URL_TIGOFRAUDMANAGEMENTSERVICE: https://gatewayhome.tigo.com.co:30443/customervalidation/v1
    ENVIRONMENT_URL_TIGORISKASSESSMENTSERVICE: https://gatewayhome.tigo.com.co:30443/customervalidation/v1
    ENVIRONMENT_URL_SERVICECOVERAGE: https://devapi-management.tigo.cam/homepassed/coberturaglx
    ENVIRONMENT_URL_SERVICECOVERAGEDGI: https://devapi-management.tigo.cam/homepassed/coberturaglx
    ### FIELD SERVICE AR2 ###
    ENVIRONMENT_URL_TIMEINTERVALFIELDSERVICE: https://fs-ar2-test.fsmillicom.com:8080/wrapper/soapWS/timeInterval.wsdl
    ENVIRONMENT_URL_PROCESSTASKEXFIELDSERVICE: https://fs-ar2-test.fsmillicom.com:8080/wrapper/soapWS/processTaskEx.wsdl
    ENVIRONMENT_URL_MULTIPLEOPERATIONSSERVICE: https://fs-ar2-test.fsmillicom.com:8080/wrapper/soapWS/multipleOperations.wsdl
    ##ENVIRONMENT_URL_GETTASK: http://castlemock.mocks.svc:8080/castlemock/mock/soap/project/3Gaohp/getTaskPortSoap11
    ENVIRONMENT_URL_GETTASK: https://fs-ar2-test.fsmillicom.com:8080/wrapper/soapWS/getTask.wsdl
      
    
    ENVIRONMENT_URL_TIGONOTIFICATIONSCIFRATORSERVICE: https://devapi-management.tigo.cam
    ### CACHE DURATION ###
    TIMETOLIVE_CACHE_CONTACTSSERVICE: '18000000'
    ### SPRING ###
    SPRING_PROFILES_ACTIVE: keycloak-monotenant
    ### SPRING ADMIN ###
    SPRING_BOOT_ADMIN_CLIENT_ENABLED: true
    SPRING_BOOT_ADMIN_CLIENT_URL: http://tigo-sales-facade:8080
    SPRING_BOOT_ADMIN_CLIENT_INSTANCE_SERVICE_BASE_URL: http://tigo-sales-facade:8080

    ### METRIC ###
    ## KEYCLOAK ##
    KEYCLOAK_REALMS_RESOURCE_0: galaxion-malta-sales-facade
    KEYCLOAK_REALMS_SECRET_0: '**********'

    MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "*"

    SECURITY_KEYCLOAK_URL: https://glx-iam-pa-stg.tigo.cam/auth
    SECURITY_KEYCLOAK_REALM: galaxion
    SECURITY_KEYCLOAK_RESOURCE: galaxion-malta-sales-facade
    SECURITY_KEYCLOAK_SECRET: “**********”

    # MOCK
    ENVIRONMENT_URL_MALTACREDITSCORESSERVICE: malta-custom-mock-service:8080

    # RABBIT
    SPRING_RABBITMQ_USERNAME: Gallaxion
    SPRING_RABBITMQ_PASSWORD: QAZ2024!
    SPRING_RABBITMQ_ADDRESSES: ************
    SPRING_RABBITMQ_VIRTUALHOST: /pa

    SPRING_CLOUD_STREAM_RABBIT_DEFAULT_EXCHANGETYPE: headers

    # UNMOCK
    # ENVIRONMENT_URL_MALTACREDITSCORESSERVICE: https://credit-vetting-service.api.dev.mla.epicmt.internal

    ### GET TOKEN MALTA OAUTH ###
    ANOTHER_KEY: "another_value"
    ################################################################################
    # GET TOKEN PRIVATE OAUTH (Used to retrieve the token before a feign call when there is none)
    ################################################################################
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_AUTHORIZATIONGRANTTYPE: client_credentials
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTID: galaxion-malta-workflow-engine-facade
    SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_KEYCLOAK_CLIENTSECRET: '**********'
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_AUTHORIZATIONURI: https://glx-iam-pa-stg.tigo.cam/auth
    SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_KEYCLOAK_TOKENURI: https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token

    # COLOMBIA
    DEFAULT_STRATUM: 2

    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS_CLIENT_MESSAGETRACING_SENT: TRACE
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS_CLIENT_MESSAGETRACING_RECEIVED: TRACE
    LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WS: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_INTERNAL_WS_TRANSPORT_HTTP_CLIENT_HTTPTRANSPORTPIPE: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_WS_TRANSPORT_HTTP_HTTPADAPTER: DEBUG
    LOGGING_LEVEL_COM_SUN_XML_WS_CLIENT_SEI: DEBUG

    #ALFRESCO WORKING PROXY
    ALFRESCO_PROXY_HOST: framework.tigo.com.co
    ALFRESCO_PROXY_PORT: 443

    #TIMEINTERVAL WORKING PROXY
    ENVIRONMENT_FIELDSERVICE_PROXY_HOST: ***********
    ENVIRONMENT_FIELDSERVICE_PROXY_PORT: 8080

    # PROXY
    ENVIRONMENT_FIELDSERVICE_USEPROXY: false
    ENVIRONMENT_TIMEINTERVAL_USEPROXY: false
    ALFRESCO_PROXY_USEPROXY: true
    #VARIABLES
    ENVIRONMENT_TIMEINTERVAL_COUNTRYID: Panama
    ENVIRONMENT_TIMEINTERVAL_NUMBER: 0
    ENVIRONMENT_TIMEINTERVAL_AREA: PA-B2C
    ENVIRONMENT_TIMEINTERVAL_DISTRICT: PA-SAN FRANCISCO PTY
    ENVIRONMENT_TIMEINTERVAL_PRIORITY: 1

    ENVIRONMENT_URL_OTP_MANAGEMENT_TOKEN: https://id.tigo.com/tigoid/pub/v2/country/CO/phone/{MSISDN}/otp
    ENVIRONMENT_URL_OTP_MANAGEMENT_CODE: https://id.tigo.com/tigoid/pub/v2/country/CO/phone/{MSISDN}/otp

    ENVIRONMENT_URL_WORKFLOWENGINEFACADE: http://workflow-engine-facade:8080

    WFE_SIGNAL_SP_WAIT_END_OF_VISIT: WAIT_END_OF_VISIT


    MANAGEMENT_OTP_SERVICE_URL: https://devapi-management.tigo.cam/otp
    

    
