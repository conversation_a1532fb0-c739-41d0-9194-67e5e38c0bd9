apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: order-status-facade
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
  - chart: order-status-facade
    helm:
      valueFiles:
      - $values/microservices/tigo/order-status-facade/values.yaml
    ref: helm
    repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
    targetRevision: "0.0.1-SNAPSHOT"
  - ref: values
    repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
    targetRevision: stg
