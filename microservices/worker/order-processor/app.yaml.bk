apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: tigo-pa-order-processor
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - chart: worker-order-processor
      helm:
        valueFiles:
          - $values/microservices/worker/order-processor/values.yaml
      ref: helm
      repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
      targetRevision: "x.x.x-SNAPSHOT"
    - ref: values
      repoURL: https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git
      targetRevision: stg
