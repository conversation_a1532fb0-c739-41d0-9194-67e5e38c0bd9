main:
  replicas: 1
  securityContext: disabled
  useConfigMap: false
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-co/backend/order-processor
    tag: 1.0.0-SNAPSHOT
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: risf
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "100m"
      memory: "512Mi"


configuration:
  envSecret:
    #CONFIGURACIONES PARA DELETE ADDON
    DATASOURCES_DB_ADDONS_USERNAME: maria-database/username
    DATASOURCES_DB_ADDONS_PASSWORD: maria-database/password
    #CONFIGURACIONES PARA DELETE ADDON
    DATASOURCES_DB_SUBSCRIPTIONCUSTOM_USERNAME: maria-database/username
    DATASOURCES_DB_SUBSCRIPTIONCUSTOM_PASSWORD: maria-database/password

  env:
    ### SERVER ###
    SERVICE_WFE_URL: workflow-engine-facade:8080
    #CONFIGURACIONES PARA DELETE ADDON
    CRON_DELETEADDONS: "*/30 * * * * ?"
    DATASOURCES_DB_ADDONS_URL: **************************,************:3306/addons
    #CONFIGURACIONES PARA DELETE ADDON
    CRON_CANCELSUSPEND: "*/30 * * * * ?"
    DATASOURCES_DB_SUBSCRIPTIONCUSTOM_URL: **************************,************:3306/subscribers_custom
    
