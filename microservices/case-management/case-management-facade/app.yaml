apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: case-management-facade-pa
  namespace: argocd
spec:
  destination:
    namespace: glx-pa
    server: https://kubernetes.default.svc
  project: glx-pa
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
  sources:
    - chart: case-management-facade
      helm:
        valueFiles:
          - $values/microservices/case-management/case-management-facade/values.yaml
      ref: helm
      repoURL: https://nexus-tsf.tigo.cam/repository/helm-charts/
      targetRevision: "1.0.0"
    - ref: values
      repoURL: "https://gitlab-tsf.tigo.cam/solutions/galaxion/crm-pa.git"
      targetRevision: stg
