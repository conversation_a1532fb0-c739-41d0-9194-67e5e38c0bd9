main:
  replicas: 1
  securityContext: enabled
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/tsf-casemanagement/backend/case-management-users-worker
    tag: release-4.0.0

  port: 8080
  alivePath: /api/actuator/health
  readyPath: /api/actuator/health
  labels:
    owner: TSF
  resources:
    limits:
      cpu: "250m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"


configuration:
  envFrom:
    - configMapRef:
        name: keycloak-access
    - secretRef:
        name: maria-database

  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,172.16.79.10:3306/user_management
    ### KEYCLOAK ###
    KEYCLOAK_AUTH_SERVER_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_RESOURCE: galaxion-case-management
    KEYCLOAK_USERNAME: cms-admin
    KEYCLOAK_PASSWORD: admin123
    KEYCLOAK_ADMIN_CLIENT_ID: admin-cli
    KEYCLOAK_CASE_MANAGMENT_CLIENT_ID: 5b0891ff-0888-43a9-8ea2-89fc99ac611a
    
