main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/tsf-casemanagement/frontend/case-management-ui
    tag: release-4.0.0
  port: 8080
  alivePath: /
  readyPath: /
  labels:
    owner: itsf
  resources:
    limits:
      cpu: "300m"
      memory: "512Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"
configuration:
  env:
    HOST: https://crm-api.tigo.com.gt
    CASE_MANAGEMENT_BACKEND_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/case-management-service/api/v1/caseManagement
    KEYCLOAK_CLIENT: galaxion-crm-ui
    KEYCLOAK_REALM: galaxion
    KEYCLOAK_URL: https://glx-iam-pa-stg.tigo.cam/auth
    KEYCLOAK_JS_URL: https://glx-iam-pa-stg.tigo.cam/auth/js/keycloak.js
    SEARCH_ENGINE_BACKEND_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/search-engine/api/v1
    CASE_MANAGEMENT_TASK_BACKEND_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/case-management-task-service/api/v1/caseTasks
    REACT_APP_USERS_MANAGEMENT_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/users-management/api/v1/usersManagement
    KEYCLOAK_CASE_MANAGEMENT_CLIENT: galaxion-case-management
    COUNTRY_CODE: PA
    CASE_MANAGEMENT_USERS_WORKER_BACKEND_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/case-management-users-worker/api/v1/caseManagementWorker
    CASE_MANAGEMENT_DOCUMENT_TYPES_API_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/tigo-sales-facade/api/v1
    CASE_MANAGEMENT_CONTACT_API_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/contact-management/api/v2/private/auth
    CASE_MANAGEMENT_FACADE_URL: https://glx-crm-pa-stg.tigo.cam/api-gateway/case-management-documents/api/v1/attachments
