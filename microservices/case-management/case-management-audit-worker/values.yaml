main:
  replicas: 1
  securityContext: enabled
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/tsf-casemanagement/backend/case-management-audit-worker
    tag: release-4.0.0

  port: 8080
  alivePath: /api/actuator/health
  readyPath: /api/actuator/health
  labels:
    owner: TSF
  resources:
    limits:
      cpu: "250m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"


configuration:
  envFrom:
    - configMapRef:
        name: keycloak-access
    - secretRef:
        name: maria-database

  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,172.16.79.10:3306/case_management
    ### DATABASE_TASK ###
    SPRING_DATASOURCE_TASK_URL: **************************,172.16.79.10:3306/case_tasks
    DB_TASK_USERNAME: ${SPRING_DATASOURCE_USERNAME}
    DB_TASK_PASSWORD: ${SPRING_DATASOURCE_PASSWORD}
    CASE_API_BASE_URL: http://case-management-pa-service:8080
    ENABLE_AUTO_CLOSE_CASES: true
    ENABLE_AUTO_HIGH_PRIORITY: true
    ENABLE_TASK_DEADLINE_NOTIFICATIONS: true
    DEADLINE_NOTIFICATIONS_DAYS_BEFORE: 5

    ### NOTIFICATION ###
    GALAXION_NOTIFICATION_URL: http://notifications-service:8080/api/v1/notifications
    GALAXION_USER_TYPE: SYSTEM
    GALAXION_USER_IDENTIFIER: SYSTEM
    GALAXION_TASK_DEADLINE_TEMPLATE: CASE_TASK_DEADLINE_NOTIFICATION
    GALAXION_NOTIFICATION_LOCALE: es_PA
    GALAXION_TIMEOUT: 5000
    
