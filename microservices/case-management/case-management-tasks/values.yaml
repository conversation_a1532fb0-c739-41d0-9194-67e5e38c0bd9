main:
  replicas: 1
  securityContext: enabled
  useConfigMap: true
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/tsf-casemanagement/backend/case-management-tasks
    tag: release-4.0.0

  port: 8080
  alivePath: /api/actuator/health
  readyPath: /api/actuator/health
  labels:
    owner: TSF
  resources:
    limits:
      cpu: "250m"
      memory: "1024Mi"
    requests:
      cpu: "50m"
      memory: "256Mi"

configuration:
  #envFrom:
  #  - configMapRef:
  #      name: keycloak-access
  envFrom:
    - secretRef:
        name: maria-database
  env:
    ### SERVER ###
    JAVA_TOOL_OPTIONS: -Xms1024m -Xmx1024m
    SERVER_PORT: 8080
    ### LOGS ###
    LOGGING_LEVEL_FR_NJJ_GALAXION: INFO
    ### DATABASE ###
    SPRING_DATASOURCE_URL: **************************,172.16.79.10:3306/case_tasks
    CASE_API_BASE_URL: http://case-management-pa-service:8080
